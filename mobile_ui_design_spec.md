# Mobile UI Design Specification for Room Management System

## Overview
This document outlines the mobile-friendly UI design for converting the desktop room management system to Android using Kivy.

## Design Principles

### 1. Touch-First Interface
- **Minimum Touch Target Size**: 44dp (density-independent pixels)
- **Spacing**: Minimum 8dp between interactive elements
- **Gestures**: Support swipe, tap, long-press, and pinch-to-zoom where appropriate

### 2. Mobile Screen Adaptation
- **Portrait-First Design**: Optimized for portrait orientation
- **Responsive Layout**: Adapts to different screen sizes (phones, tablets)
- **Safe Areas**: Respect system UI areas (status bar, navigation bar)

### 3. Navigation Patterns
- **Bottom Navigation**: Primary navigation at bottom for thumb accessibility
- **Floating Action Button (FAB)**: For primary actions like "Add Room"
- **Drawer Navigation**: Secondary navigation for settings and management

## Screen Layout Design

### 1. Main Dashboard Screen
```
┌─────────────────────────────────────┐
│ [≡] Room Management System    [⚙️]  │ ← Header with menu and settings
├─────────────────────────────────────┤
│ 📊 Financial Summary Card          │ ← Collapsible summary
│ 💰 Total Rent: ¥12,345            │
│ 🏦 Total Deposit: ¥67,890         │
│ ⚡ Total Electricity: 1,234 kWh   │
├─────────────────────────────────────┤
│ 🔍 Search Rooms...                 │ ← Search bar
├─────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐    │ ← Room cards (2 per row)
│ │ 🏠 Room 101 │ │ ✅ Room 102 │    │
│ │ Status: 闲置 │ │ Status: 租赁中│    │
│ │ ¥888/month  │ │ ¥1200/month │    │
│ └─────────────┘ └─────────────┘    │
│ ┌─────────────┐ ┌─────────────┐    │
│ │ ⚠️ Room 103 │ │ 🏠 Room 104 │    │
│ │ Status: 已到期│ │ Status: 闲置 │    │
│ │ ¥950/month  │ │ ¥750/month  │    │
│ └─────────────┘ └─────────────┘    │
├─────────────────────────────────────┤
│ [🏠 Rooms] [👥 Tenants] [📊 Reports]│ ← Bottom navigation
└─────────────────────────────────────┘
                 [+] ← FAB for adding rooms
```

### 2. Room Detail Screen
```
┌─────────────────────────────────────┐
│ [←] Room 101 Details          [⋮]  │ ← Back button and menu
├─────────────────────────────────────┤
│ 🏠 Room 101                        │ ← Room header
│ Status: 租赁中 ✅                   │
├─────────────────────────────────────┤
│ 💰 Financial Information           │ ← Expandable sections
│ ├ Monthly Rent: ¥1,200            │
│ ├ Deposit: ¥2,400                 │
│ └ Electricity: 45 kWh             │
├─────────────────────────────────────┤
│ 👥 Tenant Information             │
│ ├ Name: 张三                       │
│ ├ Phone: 138-0000-0000            │
│ └ ID: 123456789012345678          │
├─────────────────────────────────────┤
│ 📅 Rental Period                  │
│ ├ Start Date: 2025-01-01          │
│ ├ End Date: 2025-12-31            │
│ └ Days Remaining: 165             │
├─────────────────────────────────────┤
│ 📝 Notes                          │
│ └ [Tap to add notes...]           │
├─────────────────────────────────────┤
│ [Edit Room] [Extend Lease] [Checkout]│ ← Action buttons
└─────────────────────────────────────┘
```

### 3. Add/Edit Room Screen
```
┌─────────────────────────────────────┐
│ [×] Add New Room              [✓]  │ ← Cancel and save
├─────────────────────────────────────┤
│ Room Number                        │
│ ┌─────────────────────────────────┐ │
│ │ 105                             │ │ ← Input fields
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ Monthly Rent (¥)                   │
│ ┌─────────────────────────────────┐ │
│ │ 1200                            │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ Deposit (¥)                        │
│ ┌─────────────────────────────────┐ │
│ │ 2400                            │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ Electricity Reading (kWh)          │
│ ┌─────────────────────────────────┐ │
│ │ 0                               │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ Notes                              │
│ ┌─────────────────────────────────┐ │
│ │ Optional notes...               │ │
│ │                                 │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

## Color Scheme (Mobile Optimized)

### Primary Colors
- **Primary Blue**: #1e3a8a (Professional, trustworthy)
- **Primary Light**: #3b82f6 (Interactive elements)
- **Success Green**: #059669 (Rented rooms, positive actions)
- **Warning Orange**: #d97706 (Expiring leases, attention needed)
- **Danger Red**: #dc2626 (Expired leases, critical actions)

### Background Colors
- **Background**: #f8fafc (Light, easy on eyes)
- **Card Background**: #ffffff (Clean, distinct)
- **Surface**: #f1f5f9 (Subtle separation)

### Text Colors
- **Primary Text**: #1e293b (High contrast)
- **Secondary Text**: #64748b (Supporting information)
- **Muted Text**: #94a3b8 (Hints, placeholders)

## Typography (Mobile Optimized)

### Font Sizes (SP - Scale-independent Pixels)
- **Large Title**: 24sp (Screen headers)
- **Title**: 20sp (Card titles, section headers)
- **Headline**: 18sp (Important information)
- **Body**: 16sp (Main content, readable)
- **Caption**: 14sp (Supporting text, labels)
- **Small**: 12sp (Hints, metadata)

### Font Weights
- **Bold**: Titles, important information
- **Medium**: Section headers, labels
- **Regular**: Body text, descriptions

## Interactive Elements

### Buttons
- **Primary Button**: Blue background, white text, 48dp height
- **Secondary Button**: White background, blue border, blue text
- **Danger Button**: Red background, white text
- **Icon Button**: 48dp touch target, 24dp icon

### Cards
- **Elevation**: 2dp for subtle depth
- **Corner Radius**: 8dp for modern look
- **Padding**: 16dp internal padding
- **Margin**: 8dp between cards

### Input Fields
- **Height**: 56dp minimum
- **Border**: 1dp solid border, 2dp when focused
- **Corner Radius**: 4dp
- **Padding**: 16dp horizontal, 16dp vertical

## Responsive Breakpoints

### Phone (Portrait)
- **Width**: 360dp - 480dp
- **Cards per row**: 2
- **Navigation**: Bottom navigation bar

### Phone (Landscape)
- **Width**: 640dp - 854dp
- **Cards per row**: 3-4
- **Navigation**: Side navigation drawer

### Tablet
- **Width**: 600dp+
- **Cards per row**: 3-4
- **Navigation**: Side navigation drawer
- **Layout**: Master-detail view for room management

## Accessibility Features

### Touch Accessibility
- **Minimum touch target**: 44dp
- **Clear visual feedback**: Ripple effects, state changes
- **Consistent interaction patterns**: Same gestures for similar actions

### Visual Accessibility
- **High contrast ratios**: WCAG AA compliant
- **Clear visual hierarchy**: Size, color, spacing
- **Status indicators**: Icons + text for room status

### Content Accessibility
- **Content descriptions**: For screen readers
- **Clear labels**: All input fields labeled
- **Error messages**: Clear, actionable feedback

## Animation and Transitions

### Screen Transitions
- **Duration**: 300ms for screen changes
- **Easing**: Material Design standard curves
- **Direction**: Consistent navigation flow

### Micro-interactions
- **Button press**: 150ms ripple effect
- **Card tap**: 200ms elevation change
- **Loading states**: Skeleton screens or progress indicators

## Data Display Optimization

### Financial Information
- **Large, readable numbers**: 20sp+ for amounts
- **Currency symbols**: Clear ¥ symbol
- **Color coding**: Green for income, red for expenses

### Status Indicators
- **Icons + Text**: 🏠 闲置, ✅ 租赁中, ⚠️ 已到期
- **Color coding**: Blue (available), Green (rented), Red (expired)
- **Badge indicators**: Number of expired rooms

### Date Information
- **Relative dates**: "3 days remaining", "Expired 5 days ago"
- **Clear formatting**: YYYY-MM-DD for precision
- **Visual timeline**: Progress bars for lease duration
