# Testing and Debugging Guide for Android Room Management System

## Overview
This guide covers testing the Android version of the Room Management System on emulators and real devices, along with debugging common issues.

## Prerequisites for Testing

### 1. Development Environment
- Python 3.8+ installed
- Android SDK installed
- ADB (Android Debug Bridge) available
- Virtual environment set up

### 2. Testing Devices
- **Android Emulator**: API level 21+ (Android 5.0+)
- **Physical Device**: Android 5.0+ with USB debugging enabled

## Setting Up Testing Environment

### 1. Install Testing Dependencies
```bash
# Activate virtual environment
source venv/bin/activate  # Linux/macOS
# or
venv\Scripts\activate     # Windows

# Install testing dependencies
pip install pytest pytest-kivy
```

### 2. Enable Developer Options on Physical Device
1. Go to **Settings** > **About phone**
2. Tap **Build number** 7 times
3. Go back to **Settings** > **Developer options**
4. Enable **USB debugging**
5. Connect device via USB and authorize computer

### 3. Set Up Android Emulator
1. Open Android Studio
2. Go to **Tools** > **AVD Manager**
3. Create new virtual device:
   - Device: Pixel 4 or similar
   - API Level: 28+ (Android 9.0+)
   - RAM: 2GB minimum
4. Start the emulator

## Testing Procedures

### 1. Desktop Testing (Development)
Before building for Android, test the application on desktop:

```bash
# Run the application directly
python main.py

# Test specific components
python -m pytest tests/ -v
```

**Test Cases:**
- [ ] Application starts without errors
- [ ] Room cards display correctly
- [ ] Navigation between screens works
- [ ] Data persistence functions
- [ ] Financial calculations are accurate
- [ ] Touch interactions respond properly

### 2. Android Emulator Testing

#### Build and Install
```bash
# Build debug APK
python build_android.py debug

# Install on emulator
python build_android.py install

# Or run directly
python build_android.py run
```

#### Test Cases for Emulator
- [ ] App installs successfully
- [ ] App launches without crashing
- [ ] UI elements scale properly for screen size
- [ ] Touch interactions work correctly
- [ ] Data files are created in correct location
- [ ] App survives orientation changes
- [ ] Memory usage is reasonable

### 3. Physical Device Testing

#### Install APK
```bash
# Check connected devices
python build_android.py devices

# Install on specific device
python build_android.py install -d DEVICE_ID

# Or manually install
adb install bin/roommanagement-1.0.0-debug.apk
```

#### Test Cases for Physical Device
- [ ] App installs on various Android versions
- [ ] Performance is acceptable on low-end devices
- [ ] Battery usage is reasonable
- [ ] App works with different screen sizes
- [ ] Data persists after app restart
- [ ] App handles interruptions (calls, notifications)

## Common Issues and Solutions

### 1. Build Issues

#### Issue: "Buildozer command not found"
**Solution:**
```bash
pip install buildozer
# or
pip install --user buildozer
export PATH=$PATH:~/.local/bin
```

#### Issue: "Java not found"
**Solution:**
```bash
# Install OpenJDK
sudo apt install openjdk-11-jdk  # Ubuntu/Debian
brew install openjdk@11          # macOS

# Set JAVA_HOME
export JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64
```

#### Issue: "Android SDK not found"
**Solution:**
```bash
# Set Android SDK path
export ANDROID_HOME=~/Android/Sdk
export PATH=$PATH:$ANDROID_HOME/platform-tools
```

### 2. Runtime Issues

#### Issue: App crashes on startup
**Debug Steps:**
1. Check logcat output:
   ```bash
   adb logcat | grep python
   ```
2. Look for Python exceptions in logs
3. Check if all dependencies are included in buildozer.spec
4. Verify data directory permissions

#### Issue: UI elements not displaying correctly
**Debug Steps:**
1. Test on different screen densities
2. Check if dp units are used consistently
3. Verify color values are valid
4. Test with different Android versions

#### Issue: Data not persisting
**Debug Steps:**
1. Check storage permissions in buildozer.spec
2. Verify data directory path is correct
3. Test file write permissions
4. Check if external storage is available

### 3. Performance Issues

#### Issue: App is slow or laggy
**Solutions:**
1. Optimize image sizes and formats
2. Reduce number of widgets in layouts
3. Use efficient data structures
4. Profile memory usage
5. Optimize database queries

#### Issue: High memory usage
**Solutions:**
1. Release unused objects
2. Optimize image loading
3. Use lazy loading for large datasets
4. Monitor memory leaks

## Debugging Tools and Techniques

### 1. Logging
Add logging to your Python code:
```python
from kivy.logger import Logger

Logger.info("App: Starting room management system")
Logger.debug(f"App: Loading {len(rooms)} rooms")
Logger.error(f"App: Failed to save data: {error}")
```

View logs:
```bash
adb logcat | grep "python\|kivy"
```

### 2. Remote Debugging
Enable remote debugging in main.py:
```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Add debug prints
print(f"DEBUG: Rooms loaded: {len(self.rooms)}")
```

### 3. Performance Profiling
Monitor app performance:
```bash
# Monitor CPU usage
adb shell top | grep roommanagement

# Monitor memory usage
adb shell dumpsys meminfo com.roommanagement.app

# Monitor battery usage
adb shell dumpsys batterystats | grep roommanagement
```

### 4. Network Debugging (if applicable)
```bash
# Monitor network traffic
adb shell tcpdump -i any -w /sdcard/capture.pcap
```

## Test Automation

### 1. Unit Tests
Create unit tests for business logic:
```python
# tests/test_data_manager.py
import pytest
from data_manager import DataManager

def test_room_creation():
    dm = DataManager()
    room_data = {
        'id': 1,
        'room_number': '101',
        'rent': 1200.0,
        'status': '闲置'
    }
    assert dm.save_room(room_data) == True
    assert dm.get_room_by_id(1) is not None
```

### 2. UI Tests
Test UI components:
```python
# tests/test_ui.py
from kivy.tests.common import GraphicUnitTest
from main import RoomManagementApp

class TestRoomCard(GraphicUnitTest):
    def test_room_card_creation(self):
        app = RoomManagementApp()
        # Test room card creation and display
```

### 3. Integration Tests
Test complete workflows:
```python
def test_room_rental_workflow():
    # Test complete room rental process
    # 1. Create room
    # 2. Add tenant
    # 3. Rent room
    # 4. Verify data persistence
    pass
```

## Performance Benchmarks

### Target Performance Metrics
- **App startup time**: < 3 seconds
- **Screen transition time**: < 500ms
- **Memory usage**: < 100MB for typical usage
- **Battery usage**: < 5% per hour of active use
- **APK size**: < 50MB

### Measuring Performance
```bash
# Measure app startup time
adb shell am start -W com.roommanagement.app/.MainActivity

# Monitor memory usage over time
adb shell dumpsys meminfo com.roommanagement.app | grep TOTAL
```

## Release Testing Checklist

Before releasing the application:

### Functionality Testing
- [ ] All core features work correctly
- [ ] Data persistence works across app restarts
- [ ] Financial calculations are accurate
- [ ] All screens are accessible and functional
- [ ] Error handling works properly

### Compatibility Testing
- [ ] Works on Android 5.0+ (API 21+)
- [ ] Works on different screen sizes (phone, tablet)
- [ ] Works on different screen densities
- [ ] Works with different Android versions

### Performance Testing
- [ ] App starts within 3 seconds
- [ ] Smooth scrolling and navigation
- [ ] Reasonable memory usage
- [ ] No memory leaks detected
- [ ] Battery usage is acceptable

### Security Testing
- [ ] Data files are stored securely
- [ ] No sensitive information in logs
- [ ] Proper permission handling
- [ ] Input validation works correctly

### User Experience Testing
- [ ] UI is intuitive and easy to use
- [ ] Touch targets are appropriately sized
- [ ] Text is readable on all screen sizes
- [ ] App follows Android design guidelines
- [ ] Accessibility features work properly

## Troubleshooting Common Test Failures

### 1. Import Errors
```
ModuleNotFoundError: No module named 'kivymd'
```
**Solution:** Add missing modules to buildozer.spec requirements

### 2. Permission Errors
```
PermissionError: [Errno 13] Permission denied
```
**Solution:** Add required permissions to buildozer.spec

### 3. File Not Found Errors
```
FileNotFoundError: [Errno 2] No such file or directory: 'data/rooms.json'
```
**Solution:** Ensure data files are included in the APK or create them at runtime

### 4. UI Layout Issues
**Solution:** Use dp units, test on different screen sizes, check orientation handling

## Continuous Testing

Set up automated testing:
1. Use GitHub Actions or similar CI/CD
2. Run tests on every commit
3. Build APK automatically
4. Test on multiple Android versions
5. Generate test reports

This comprehensive testing approach ensures your Android Room Management System works reliably across different devices and scenarios.
