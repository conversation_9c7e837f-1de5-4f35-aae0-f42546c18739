#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对话框尺寸自适应测试脚本
"""

import tkinter as tk
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入标准化对话框类
from 房间管理系统2 import StandardMessageBox

def test_dialog_sizes():
    """测试对话框尺寸自适应"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 配色方案
    colors = {
        'primary': '#1e3a8a',
        'primary_light': '#3b82f6',
        'secondary': '#059669',
        'secondary_light': '#10b981',
        'warning': '#d97706',
        'warning_light': '#f59e0b',
        'danger': '#dc2626',
        'danger_light': '#ef4444',
        'info': '#0891b2',
        'info_light': '#06b6d4',
        'background': '#f8fafc',
        'card_bg': '#ffffff',
        'text_primary': '#1e293b',
        'text_secondary': '#64748b',
        'border': '#e2e8f0',
        'white': '#ffffff',
        'medium_gray': '#e2e8f0',
        'dark_gray': '#64748b'
    }
    
    fonts = {
        'heading': ('Microsoft YaHei UI', 16, 'bold'),
        'body': ('Microsoft YaHei UI', 11),
        'button': ('Microsoft YaHei UI', 10, 'bold')
    }
    
    # 初始化消息框
    msgbox = StandardMessageBox(root, colors, fonts)
    
    print("📏 开始对话框尺寸自适应测试...")
    
    # 测试1：短消息 - 验证最小尺寸
    print("📝 测试1：短消息对话框尺寸")
    msgbox.showinfo("短消息测试", "短")
    
    # 测试2：中等长度消息
    print("📝 测试2：中等长度消息对话框尺寸")
    medium_msg = "这是一个中等长度的消息，用来测试对话框是否能够自动调整到合适的尺寸。"
    msgbox.showsuccess("中等消息测试", medium_msg)
    
    # 测试3：长消息 - 验证自动扩展
    print("📝 测试3：长消息对话框尺寸")
    long_msg = "这是一个很长的消息内容，用来测试对话框的自动尺寸调整功能。" * 8
    msgbox.showwarning("长消息测试", long_msg)
    
    # 测试4：多行消息 - 验证换行处理
    print("📝 测试4：多行消息对话框尺寸")
    multiline_msg = """多行消息测试：

第一行：基本信息
第二行：详细描述  
第三行：操作结果
第四行：注意事项
第五行：补充说明

数据统计：
- 成功项目：15个
- 失败项目：2个
- 待处理项目：3个

重要提醒：
1. 请检查所有数据的准确性
2. 确保备份重要信息
3. 及时更新系统状态
4. 定期进行系统维护"""
    
    msgbox.showerror("多行消息测试", multiline_msg)
    
    # 测试5：确认对话框 - 验证双按钮布局
    print("📝 测试5：确认对话框尺寸（短消息）")
    result1 = msgbox.askyesno("确认测试", "确定要执行此操作吗？")
    print(f"   结果：{'确定' if result1 else '取消'}")
    
    # 测试6：确认对话框 - 长消息
    print("📝 测试6：确认对话框尺寸（长消息）")
    long_confirm = """确定要删除以下所有数据吗？

删除内容包括：
• 所有房间信息（共25间房间）
• 所有租户数据（共18位租户）
• 所有租赁记录（共156条记录）
• 所有财务数据（租金、押金、电费记录）
• 所有系统设置和配置信息

警告：此操作不可撤销！
删除后将无法恢复任何数据，请谨慎操作。

如果您确定要继续，请点击"确定"按钮。
如果您想保留数据，请点击"取消"按钮。"""
    
    result2 = msgbox.askyesno("重要确认", long_confirm)
    print(f"   结果：{'确定' if result2 else '取消'}")
    
    # 测试7：极长消息 - 验证最大尺寸限制
    print("📝 测试7：极长消息对话框尺寸")
    very_long_msg = "这是一个极长的消息内容，用来测试对话框的最大尺寸限制功能。" * 20
    msgbox.showinfo("极长消息测试", very_long_msg)
    
    # 测试8：包含特殊字符的消息
    print("📝 测试8：特殊字符消息对话框尺寸")
    special_msg = """特殊字符测试：

符号测试：！@#￥%……&*（）——+
数字测试：1234567890
英文测试：ABCDEFGHIJKLMNOPQRSTUVWXYZ
中文测试：房间管理系统对话框尺寸自适应测试
混合测试：Room管理System系统Dialog对话框Test测试

表格格式：
┌─────────────────┬─────────────────┐
│ 项目名称        │ 状态            │
├─────────────────┼─────────────────┤
│ 尺寸自适应      │ ✅ 已完成       │
│ 按钮显示        │ ✅ 已完成       │
│ 布局优化        │ 🔄 进行中       │
└─────────────────┴─────────────────┘"""
    
    msgbox.showsuccess("特殊字符测试", special_msg)
    
    print("✅ 所有尺寸自适应测试完成！")
    print("\n📊 测试总结：")
    print("- 测试了8种不同长度和类型的消息")
    print("- 验证了单按钮和双按钮对话框的尺寸适应")
    print("- 检查了最小尺寸、自动扩展和最大尺寸限制")
    print("- 确认了按钮在所有尺寸下都能完整显示")
    
    root.destroy()

if __name__ == "__main__":
    test_dialog_sizes()
