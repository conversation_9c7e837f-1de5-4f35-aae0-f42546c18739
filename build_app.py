#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
房间管理系统打包脚本
使用PyInstaller将Python程序打包成独立的桌面应用程序
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
from datetime import datetime

def check_requirements():
    """检查打包环境和依赖"""
    print("🔍 检查打包环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
        print("❌ 警告: 建议使用Python 3.7或更高版本")
    else:
        print("✅ Python版本符合要求")
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller已安装: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller未安装，正在安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("✅ PyInstaller安装完成")
    
    # 检查源文件
    source_file = "房间管理系统单机版.py"
    if not os.path.exists(source_file):
        print(f"❌ 错误: 找不到源文件 {source_file}")
        return False
    else:
        print(f"✅ 源文件存在: {source_file}")
    
    # 检查数据文件
    data_files = ["rooms.json", "persons.json", "financial_data.json"]
    for file in data_files:
        if os.path.exists(file):
            print(f"✅ 数据文件存在: {file}")
        else:
            print(f"⚠️  数据文件不存在: {file} (将在运行时创建)")
    
    return True

def create_icon():
    """创建应用图标（如果不存在）"""
    icon_file = "app_icon.ico"
    if not os.path.exists(icon_file):
        print("🎨 创建应用图标...")
        try:
            from PIL import Image, ImageDraw, ImageFont
            
            # 创建一个简单的图标
            size = (256, 256)
            img = Image.new('RGBA', size, (30, 58, 138, 255))  # 深蓝色背景
            draw = ImageDraw.Draw(img)
            
            # 绘制房屋图标
            # 房屋主体
            draw.rectangle([64, 128, 192, 224], fill=(255, 255, 255, 255))
            # 屋顶
            draw.polygon([(64, 128), (128, 64), (192, 128)], fill=(220, 38, 38, 255))
            # 门
            draw.rectangle([112, 176, 144, 224], fill=(139, 69, 19, 255))
            # 窗户
            draw.rectangle([80, 144, 112, 176], fill=(135, 206, 235, 255))
            draw.rectangle([144, 144, 176, 176], fill=(135, 206, 235, 255))
            
            # 保存为ICO格式
            img.save(icon_file, format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
            print(f"✅ 图标创建完成: {icon_file}")
            return icon_file
        except ImportError:
            print("⚠️  PIL未安装，跳过图标创建")
            return None
        except Exception as e:
            print(f"⚠️  图标创建失败: {e}")
            return None
    else:
        print(f"✅ 图标文件已存在: {icon_file}")
        return icon_file

def build_application():
    """构建应用程序"""
    print("\n🔨 开始构建应用程序...")
    
    source_file = "房间管理系统单机版.py"
    app_name = "房间管理系统"
    
    # 基本PyInstaller命令
    cmd = [
        "pyinstaller",
        "--onefile",  # 打包成单个文件
        "--windowed",  # 无控制台窗口
        "--name", app_name,  # 应用程序名称
        "--clean",  # 清理临时文件
    ]
    
    # 添加图标
    icon_file = create_icon()
    if icon_file and os.path.exists(icon_file):
        cmd.extend(["--icon", icon_file])
    
    # 添加数据文件
    data_files = ["rooms.json", "persons.json", "financial_data.json"]
    for file in data_files:
        if os.path.exists(file):
            cmd.extend(["--add-data", f"{file};."])
    
    # 添加隐藏导入（如果需要）
    hidden_imports = [
        "tkinter",
        "tkinter.ttk",
        "tkinter.messagebox",
        "tkinter.simpledialog",
        "datetime",
        "json",
        "os"
    ]
    
    for module in hidden_imports:
        cmd.extend(["--hidden-import", module])
    
    # 添加源文件
    cmd.append(source_file)
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        # 执行打包命令
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 打包成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def post_build_tasks():
    """打包后的处理任务"""
    print("\n📦 执行打包后处理...")
    
    dist_dir = Path("dist")
    app_name = "房间管理系统.exe"
    
    if dist_dir.exists() and (dist_dir / app_name).exists():
        app_path = dist_dir / app_name
        print(f"✅ 应用程序已生成: {app_path}")
        print(f"📁 文件大小: {app_path.stat().st_size / 1024 / 1024:.1f} MB")
        
        # 创建发布目录
        release_dir = Path("release")
        if release_dir.exists():
            shutil.rmtree(release_dir)
        release_dir.mkdir()
        
        # 复制可执行文件
        shutil.copy2(app_path, release_dir / app_name)
        
        # 复制数据文件（如果存在）
        data_files = ["rooms.json", "persons.json", "financial_data.json"]
        for file in data_files:
            if os.path.exists(file):
                shutil.copy2(file, release_dir / file)
        
        # 创建使用说明
        create_readme(release_dir)
        
        print(f"✅ 发布包已准备完成: {release_dir}")
        return True
    else:
        print("❌ 未找到生成的应用程序")
        return False

def create_readme(release_dir):
    """创建使用说明文件"""
    readme_content = """# 房间管理系统

## 使用说明

### 系统要求
- Windows 7/8/10/11 (64位)
- 无需安装Python环境

### 安装和运行
1. 解压所有文件到任意目录
2. 双击 `房间管理系统.exe` 启动程序
3. 首次运行会自动创建必要的数据文件

### 功能特性
- 房间信息管理
- 租户信息管理
- 租房流程管理
- 财务数据统计
- 现代化用户界面

### 数据文件说明
- `rooms.json` - 房间信息数据
- `persons.json` - 租户信息数据
- `financial_data.json` - 财务统计数据

### 注意事项
1. 请定期备份数据文件
2. 不要删除或修改数据文件
3. 如遇问题，请重新启动程序

### 技术支持
如有问题，请联系系统管理员。

---
版本: 2.0
构建日期: """ + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + """
"""
    
    readme_path = release_dir / "使用说明.txt"
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✅ 使用说明已创建: {readme_path}")

def cleanup():
    """清理临时文件"""
    print("\n🧹 清理临时文件...")
    
    cleanup_dirs = ["build", "__pycache__"]
    cleanup_files = ["房间管理系统.spec"]
    
    for dir_name in cleanup_dirs:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✅ 已删除: {dir_name}")
    
    for file_name in cleanup_files:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"✅ 已删除: {file_name}")

def main():
    """主函数"""
    print("🏨 房间管理系统打包工具")
    print("=" * 50)
    
    try:
        # 检查环境
        if not check_requirements():
            print("❌ 环境检查失败，请解决问题后重试")
            return False
        
        # 构建应用
        if not build_application():
            print("❌ 应用构建失败")
            return False
        
        # 后处理
        if not post_build_tasks():
            print("❌ 后处理失败")
            return False
        
        # 清理
        cleanup()
        
        print("\n🎉 打包完成!")
        print("📁 发布文件位于 'release' 目录")
        print("💡 可以将 'release' 目录中的文件分发给用户")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️  用户取消操作")
        return False
    except Exception as e:
        print(f"\n❌ 打包过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
