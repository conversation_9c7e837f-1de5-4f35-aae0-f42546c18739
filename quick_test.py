#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证标准化对话框修复效果的测试脚本
"""

import tkinter as tk
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入标准化对话框类
from 房间管理系统2 import StandardMessageBox

def quick_test():
    """快速测试所有对话框类型"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 配色方案
    colors = {
        'primary': '#1e3a8a',
        'secondary': '#059669',
        'warning': '#d97706',
        'danger': '#dc2626',
        'info': '#0891b2',
        'background': '#f8fafc',
        'card_bg': '#ffffff',
        'text_primary': '#1e293b',
        'text_secondary': '#64748b',
        'border': '#e2e8f0',
        'white': '#ffffff',
        'medium_gray': '#e2e8f0',
        'dark_gray': '#64748b'
    }
    
    fonts = {
        'heading': ('Microsoft YaHei UI', 16, 'bold'),
        'body': ('Microsoft YaHei UI', 11),
        'button': ('Microsoft YaHei UI', 10, 'bold')
    }
    
    # 初始化消息框
    msgbox = StandardMessageBox(root, colors, fonts)
    
    print("🧪 开始快速测试标准化对话框...")
    
    # 测试1：短消息
    print("📝 测试1：短消息显示")
    msgbox.showsuccess("✅ 测试成功", "这是一个短消息测试")
    
    # 测试2：长消息
    print("📝 测试2：长消息显示")
    long_msg = "这是一个很长的消息内容，用来测试对话框是否能够正确处理长文本。" * 5
    msgbox.showinfo("ℹ️ 长消息测试", long_msg)
    
    # 测试3：多行消息
    print("📝 测试3：多行消息显示")
    multiline_msg = """多行消息测试：

第一行：基本信息
第二行：详细描述
第三行：操作结果

数据统计：
- 总房间：25间
- 已出租：18间
- 空闲：7间

注意：请确保数据准确性"""
    msgbox.showwarning("⚠️ 多行测试", multiline_msg)
    
    # 测试4：错误对话框
    print("📝 测试4：错误对话框显示")
    error_msg = "操作失败！\n\n错误详情：\n• 文件权限不足\n• 网络连接超时\n• 数据格式错误"
    msgbox.showerror("❌ 错误测试", error_msg)
    
    # 测试5：确认对话框
    print("📝 测试5：确认对话框显示")
    confirm_msg = "确定要删除以下房间吗？\n\n房间信息：\n• 房号：101\n• 租户：张三\n• 押金：¥3000\n\n此操作不可撤销！"
    result = msgbox.askyesno("❓ 确认测试", confirm_msg)
    print(f"   用户选择：{'确定' if result else '取消'}")
    
    print("✅ 所有测试完成！对话框显示正常。")
    root.destroy()

if __name__ == "__main__":
    quick_test()
