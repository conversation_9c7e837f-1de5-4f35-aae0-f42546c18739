"""
Data Manager for Android Room Management System
Handles data persistence with Android-compatible storage
"""

import json
import os
from kivy.storage.jsonstore import <PERSON>sonS<PERSON>
from kivy.utils import platform
from kivy.logger import Logger

class DataManager:
    """Manages data persistence for the room management system"""
    
    def __init__(self):
        self.data_dir = self.get_data_directory()
        self.ensure_data_directory()
        
        # Initialize data stores
        self.rooms_store = JsonStore(os.path.join(self.data_dir, 'rooms.json'))
        self.persons_store = JsonStore(os.path.join(self.data_dir, 'persons.json'))
        self.financial_store = JsonStore(os.path.join(self.data_dir, 'financial_data.json'))
        
        # Initialize default data
        self.init_default_data()
    
    def get_data_directory(self):
        """Get the appropriate data directory for the platform"""
        if platform == 'android':
            from android.storage import primary_external_storage_path
            # Use external storage for Android
            data_dir = os.path.join(primary_external_storage_path(), 'RoomManagement')
        else:
            # Use current directory for desktop/development
            data_dir = os.path.join(os.getcwd(), 'data')
        
        return data_dir
    
    def ensure_data_directory(self):
        """Ensure the data directory exists"""
        try:
            if not os.path.exists(self.data_dir):
                os.makedirs(self.data_dir)
                Logger.info(f"DataManager: Created data directory: {self.data_dir}")
        except Exception as e:
            Logger.error(f"DataManager: Failed to create data directory: {e}")
            # Fallback to app directory
            self.data_dir = os.getcwd()
    
    def init_default_data(self):
        """Initialize default data if stores are empty"""
        try:
            # Initialize financial data if not exists
            if not self.financial_store.exists('totals'):
                self.financial_store.put('totals',
                    total_rent_collected=0.0,
                    total_deposit_collected=0.0,
                    total_electricity_consumption=0.0
                )
                Logger.info("DataManager: Initialized default financial data")
            
            # Initialize sample room data if no rooms exist
            if len(self.rooms_store.keys()) == 0:
                self.create_sample_data()
                
        except Exception as e:
            Logger.error(f"DataManager: Failed to initialize default data: {e}")
    
    def create_sample_data(self):
        """Create sample data for demonstration"""
        try:
            # Sample rooms
            sample_rooms = [
                {
                    'id': 1,
                    'room_number': '101',
                    'rent': 1200.0,
                    'deposit': 2400.0,
                    'electricity_fee': 0,
                    'tenant_ids': [],
                    'start_date': None,
                    'end_date': None,
                    'status': '闲置',
                    'note': ''
                },
                {
                    'id': 2,
                    'room_number': '102',
                    'rent': 1500.0,
                    'deposit': 3000.0,
                    'electricity_fee': 45,
                    'tenant_ids': [1],
                    'start_date': '2025-01-01',
                    'end_date': '2025-12-31',
                    'status': '已出租',
                    'note': '优质租户'
                },
                {
                    'id': 3,
                    'room_number': '103',
                    'rent': 1000.0,
                    'deposit': 2000.0,
                    'electricity_fee': 78,
                    'tenant_ids': [2],
                    'start_date': '2024-06-01',
                    'end_date': '2025-01-15',
                    'status': '已出租',
                    'note': '需要维修空调'
                }
            ]
            
            # Save sample rooms
            for room in sample_rooms:
                self.rooms_store.put(str(room['id']), **room)
            
            # Sample persons
            sample_persons = [
                {
                    'id': 1,
                    'name': '张三',
                    'phone': '138-0000-0001',
                    'id_number': '123456789012345678',
                    'emergency_contact': '李四 139-0000-0002'
                },
                {
                    'id': 2,
                    'name': '王五',
                    'phone': '138-0000-0003',
                    'id_number': '123456789012345679',
                    'emergency_contact': '赵六 139-0000-0004'
                }
            ]
            
            # Save sample persons
            for person in sample_persons:
                self.persons_store.put(str(person['id']), **person)
            
            Logger.info("DataManager: Created sample data")
            
        except Exception as e:
            Logger.error(f"DataManager: Failed to create sample data: {e}")
    
    def get_all_rooms(self):
        """Get all rooms as a list"""
        try:
            rooms = []
            for key in self.rooms_store.keys():
                room_data = dict(self.rooms_store.get(key))
                rooms.append(room_data)
            return sorted(rooms, key=lambda x: x.get('id', 0))
        except Exception as e:
            Logger.error(f"DataManager: Failed to get rooms: {e}")
            return []
    
    def get_room_by_id(self, room_id):
        """Get a specific room by ID"""
        try:
            if self.rooms_store.exists(str(room_id)):
                return dict(self.rooms_store.get(str(room_id)))
            return None
        except Exception as e:
            Logger.error(f"DataManager: Failed to get room {room_id}: {e}")
            return None
    
    def save_room(self, room_data):
        """Save or update a room"""
        try:
            room_id = str(room_data['id'])
            self.rooms_store.put(room_id, **room_data)
            Logger.info(f"DataManager: Saved room {room_id}")
            return True
        except Exception as e:
            Logger.error(f"DataManager: Failed to save room: {e}")
            return False
    
    def delete_room(self, room_id):
        """Delete a room"""
        try:
            if self.rooms_store.exists(str(room_id)):
                self.rooms_store.delete(str(room_id))
                Logger.info(f"DataManager: Deleted room {room_id}")
                return True
            return False
        except Exception as e:
            Logger.error(f"DataManager: Failed to delete room {room_id}: {e}")
            return False
    
    def get_all_persons(self):
        """Get all persons as a list"""
        try:
            persons = []
            for key in self.persons_store.keys():
                person_data = dict(self.persons_store.get(key))
                persons.append(person_data)
            return sorted(persons, key=lambda x: x.get('id', 0))
        except Exception as e:
            Logger.error(f"DataManager: Failed to get persons: {e}")
            return []
    
    def get_person_by_id(self, person_id):
        """Get a specific person by ID"""
        try:
            if self.persons_store.exists(str(person_id)):
                return dict(self.persons_store.get(str(person_id)))
            return None
        except Exception as e:
            Logger.error(f"DataManager: Failed to get person {person_id}: {e}")
            return None
    
    def save_person(self, person_data):
        """Save or update a person"""
        try:
            person_id = str(person_data['id'])
            self.persons_store.put(person_id, **person_data)
            Logger.info(f"DataManager: Saved person {person_id}")
            return True
        except Exception as e:
            Logger.error(f"DataManager: Failed to save person: {e}")
            return False
    
    def delete_person(self, person_id):
        """Delete a person"""
        try:
            if self.persons_store.exists(str(person_id)):
                self.persons_store.delete(str(person_id))
                Logger.info(f"DataManager: Deleted person {person_id}")
                return True
            return False
        except Exception as e:
            Logger.error(f"DataManager: Failed to delete person {person_id}: {e}")
            return False
    
    def get_financial_data(self):
        """Get financial data"""
        try:
            if self.financial_store.exists('totals'):
                return dict(self.financial_store.get('totals'))
            else:
                # Return default values
                return {
                    'total_rent_collected': 0.0,
                    'total_deposit_collected': 0.0,
                    'total_electricity_consumption': 0.0
                }
        except Exception as e:
            Logger.error(f"DataManager: Failed to get financial data: {e}")
            return {
                'total_rent_collected': 0.0,
                'total_deposit_collected': 0.0,
                'total_electricity_consumption': 0.0
            }
    
    def save_financial_data(self, financial_data):
        """Save financial data"""
        try:
            self.financial_store.put('totals', **financial_data)
            Logger.info("DataManager: Saved financial data")
            return True
        except Exception as e:
            Logger.error(f"DataManager: Failed to save financial data: {e}")
            return False
    
    def get_next_room_id(self):
        """Get the next available room ID"""
        try:
            rooms = self.get_all_rooms()
            if not rooms:
                return 1
            max_id = max(room.get('id', 0) for room in rooms)
            return max_id + 1
        except Exception as e:
            Logger.error(f"DataManager: Failed to get next room ID: {e}")
            return 1
    
    def get_next_person_id(self):
        """Get the next available person ID"""
        try:
            persons = self.get_all_persons()
            if not persons:
                return 1
            max_id = max(person.get('id', 0) for person in persons)
            return max_id + 1
        except Exception as e:
            Logger.error(f"DataManager: Failed to get next person ID: {e}")
            return 1
    
    def backup_data(self):
        """Create a backup of all data"""
        try:
            backup_data = {
                'rooms': self.get_all_rooms(),
                'persons': self.get_all_persons(),
                'financial': self.get_financial_data(),
                'backup_date': str(datetime.now())
            }
            
            backup_file = os.path.join(self.data_dir, f'backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2)
            
            Logger.info(f"DataManager: Created backup: {backup_file}")
            return backup_file
            
        except Exception as e:
            Logger.error(f"DataManager: Failed to create backup: {e}")
            return None
    
    def restore_data(self, backup_file):
        """Restore data from backup"""
        try:
            with open(backup_file, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
            
            # Restore rooms
            for room in backup_data.get('rooms', []):
                self.save_room(room)
            
            # Restore persons
            for person in backup_data.get('persons', []):
                self.save_person(person)
            
            # Restore financial data
            financial_data = backup_data.get('financial', {})
            if financial_data:
                self.save_financial_data(financial_data)
            
            Logger.info(f"DataManager: Restored data from: {backup_file}")
            return True
            
        except Exception as e:
            Logger.error(f"DataManager: Failed to restore data: {e}")
            return False
