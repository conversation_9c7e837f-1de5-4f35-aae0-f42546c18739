#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试标准化对话框按钮显示效果的测试脚本
"""

import tkinter as tk
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入标准化对话框类
from 房间管理系统2 import StandardMessageBox

def test_button_display():
    """专门测试按钮显示效果"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 配色方案
    colors = {
        'primary': '#1e3a8a',
        'primary_light': '#3b82f6',
        'secondary': '#059669',
        'secondary_light': '#10b981',
        'warning': '#d97706',
        'warning_light': '#f59e0b',
        'danger': '#dc2626',
        'danger_light': '#ef4444',
        'info': '#0891b2',
        'info_light': '#06b6d4',
        'background': '#f8fafc',
        'card_bg': '#ffffff',
        'text_primary': '#1e293b',
        'text_secondary': '#64748b',
        'border': '#e2e8f0',
        'white': '#ffffff',
        'medium_gray': '#e2e8f0',
        'dark_gray': '#64748b'
    }
    
    fonts = {
        'heading': ('Microsoft YaHei UI', 16, 'bold'),
        'body': ('Microsoft YaHei UI', 11),
        'button': ('Microsoft YaHei UI', 10, 'bold')
    }
    
    # 初始化消息框
    msgbox = StandardMessageBox(root, colors, fonts)
    
    print("🔘 开始按钮显示测试...")
    
    # 测试1：单按钮对话框 - 成功类型
    print("📝 测试1：成功对话框单按钮显示")
    msgbox.showsuccess("✅ 按钮测试 - 成功", 
                      "单按钮显示测试\n\n检查项目：\n• 按钮是否完整显示\n• 按钮文字是否清晰\n• 按钮尺寸是否合适")
    
    # 测试2：单按钮对话框 - 信息类型
    print("📝 测试2：信息对话框单按钮显示")
    msgbox.showinfo("ℹ️ 按钮测试 - 信息", 
                   "单按钮显示测试（信息类型）\n\n检查项目：\n• 按钮颜色是否正确\n• 按钮位置是否居中\n• 按钮间距是否合理")
    
    # 测试3：单按钮对话框 - 警告类型
    print("📝 测试3：警告对话框单按钮显示")
    msgbox.showwarning("⚠️ 按钮测试 - 警告", 
                      "单按钮显示测试（警告类型）\n\n检查项目：\n• 按钮悬停效果是否正常\n• 按钮点击响应是否及时\n• 按钮边框是否完整")
    
    # 测试4：单按钮对话框 - 错误类型
    print("📝 测试4：错误对话框单按钮显示")
    msgbox.showerror("❌ 按钮测试 - 错误", 
                    "单按钮显示测试（错误类型）\n\n检查项目：\n• 按钮在长文本对话框中的显示\n• 按钮区域是否有足够空间\n• 按钮是否被内容遮挡")
    
    # 测试5：双按钮对话框 - 确认类型
    print("📝 测试5：确认对话框双按钮显示")
    result1 = msgbox.askyesno("❓ 按钮测试 - 确认", 
                             "双按钮显示测试\n\n检查项目：\n• 两个按钮是否都完整显示\n• 按钮间距是否合理\n• 按钮尺寸是否一致\n• 按钮对齐是否正确\n\n请点击任意按钮继续")
    
    print(f"   用户选择：{'确定' if result1 else '取消'}")
    
    # 测试6：双按钮对话框 - 长文本
    print("📝 测试6：长文本确认对话框双按钮显示")
    long_confirm_text = """这是一个包含很长文本内容的确认对话框测试。

详细说明：
这个测试用来验证当对话框包含大量文本内容时，底部的按钮是否仍然能够正确显示。我们需要确保：

1. 按钮不会被文本内容遮挡
2. 按钮区域有足够的垂直空间
3. 按钮的水平布局保持正确
4. 两个按钮的尺寸保持一致
5. 按钮之间的间距合理
6. 按钮的文字完整显示
7. 按钮的交互功能正常

操作说明：
请仔细观察对话框的布局，特别是底部按钮区域的显示效果。然后点击任意按钮来测试交互功能。

确认问题：
您认为当前的按钮显示效果是否满足要求？"""
    
    result2 = msgbox.askyesno("❓ 长文本按钮测试", long_confirm_text)
    print(f"   用户选择：{'确定' if result2 else '取消'}")
    
    # 测试7：极短文本对话框
    print("📝 测试7：极短文本对话框按钮显示")
    msgbox.showinfo("ℹ️ 短文本", "短")
    
    # 测试8：中等长度文本对话框
    print("📝 测试8：中等长度文本对话框按钮显示")
    medium_text = "这是一个中等长度的文本消息，用来测试按钮在不同文本长度下的显示效果。"
    msgbox.showwarning("⚠️ 中等文本测试", medium_text)
    
    # 测试9：多行格式化文本
    print("📝 测试9：多行格式化文本对话框按钮显示")
    formatted_text = """格式化文本测试：

项目列表：
• 第一项：测试内容
• 第二项：测试内容
• 第三项：测试内容

数据统计：
- 成功：15项
- 失败：2项
- 待处理：3项

注意事项：
1. 检查按钮显示
2. 验证布局效果
3. 确认交互功能"""
    
    result3 = msgbox.askyesno("❓ 格式化文本测试", formatted_text)
    print(f"   用户选择：{'确定' if result3 else '取消'}")
    
    # 测试完成
    print("✅ 所有按钮显示测试完成！")
    msgbox.showsuccess("🎉 测试完成", 
                      "所有按钮显示测试已完成！\n\n测试项目：\n• 单按钮对话框（4种类型）\n• 双按钮对话框（3种场景）\n• 不同文本长度适应性\n• 格式化文本显示效果\n\n如果所有按钮都能正确显示和交互，说明修复成功！")
    
    root.destroy()

if __name__ == "__main__":
    test_button_display()
