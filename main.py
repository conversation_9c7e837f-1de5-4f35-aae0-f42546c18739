"""
Room Management System - Android Version
Mobile-optimized room management application built with <PERSON>vy
"""

from kivy.app import App
from kivy.uix.screenmanager import ScreenManager, Screen
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.scrollview import ScrollView
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.uix.textinput import TextInput
from kivy.uix.popup import Popup
# from kivy.uix.card import Card  # Card is not available in standard Kivy
from kivy.metrics import dp
from kivy.clock import Clock
from kivy.storage.jsonstore import JsonStore
from kivy.utils import get_color_from_hex
from datetime import datetime, timedelta
import json
import os
from room_details_screen import RoomDetailsScreen
from data_manager import DataManager

class RoomCard(BoxLayout):
    """Custom room card widget for mobile display"""
    
    def __init__(self, room_data, app_instance, **kwargs):
        super().__init__(**kwargs)
        self.room_data = room_data
        self.app_instance = app_instance
        self.orientation = 'vertical'
        self.size_hint_y = None
        self.height = dp(180)
        self.padding = dp(8)
        self.spacing = dp(4)
        
        # Set background color based on room status
        self.setup_card_appearance()
        self.build_card_content()
        
    def setup_card_appearance(self):
        """Set card appearance based on room status"""
        today = datetime.today().date()
        end_date = None
        
        if self.room_data.get("end_date"):
            try:
                end_date = datetime.strptime(self.room_data["end_date"], "%Y-%m-%d").date()
            except ValueError:
                pass
        
        # Determine card color based on status
        if self.room_data["status"] == "已出租":
            if end_date and end_date < today:
                self.bg_color = get_color_from_hex('#dc2626')  # Red for expired
                self.status_icon = "⚠️"
                self.status_text = "已到期"
            else:
                self.bg_color = get_color_from_hex('#059669')  # Green for rented
                self.status_icon = "✅"
                self.status_text = "租赁中"
        else:
            self.bg_color = get_color_from_hex('#3b82f6')  # Blue for available
            self.status_icon = "🏠"
            self.status_text = "闲置"
            
        # Set canvas background
        with self.canvas.before:
            from kivy.graphics import Color, RoundedRectangle
            Color(*self.bg_color)
            self.rect = RoundedRectangle(pos=self.pos, size=self.size, radius=[dp(8)])
            
        self.bind(pos=self.update_rect, size=self.update_rect)
    
    def update_rect(self, *args):
        """Update background rectangle when size/position changes"""
        self.rect.pos = self.pos
        self.rect.size = self.size
    
    def build_card_content(self):
        """Build the card content layout"""
        # Header with room number and status
        header = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(40))
        
        status_label = Label(
            text=self.status_icon,
            font_size=dp(20),
            size_hint_x=None,
            width=dp(40),
            color=(1, 1, 1, 1)
        )
        
        room_number_label = Label(
            text=f"房间 {self.room_data['room_number']}",
            font_size=dp(16),
            bold=True,
            color=(1, 1, 1, 1),
            halign='left',
            valign='middle'
        )
        room_number_label.bind(size=room_number_label.setter('text_size'))
        
        header.add_widget(status_label)
        header.add_widget(room_number_label)
        self.add_widget(header)
        
        # Status line
        status_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(30))
        status_info = Label(
            text=f"状态：{self.status_text}",
            font_size=dp(14),
            color=(1, 1, 1, 1),
            halign='left',
            valign='middle'
        )
        status_info.bind(size=status_info.setter('text_size'))
        status_layout.add_widget(status_info)
        self.add_widget(status_layout)
        
        # Financial info
        finance_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(25))
        
        rent_label = Label(
            text=f"💰 ¥{self.room_data['rent']:.0f}",
            font_size=dp(12),
            color=(1, 1, 1, 1),
            size_hint_x=0.5,
            halign='left'
        )
        rent_label.bind(size=rent_label.setter('text_size'))
        
        deposit_label = Label(
            text=f"🏦 ¥{self.room_data['deposit']:.0f}",
            font_size=dp(12),
            color=(1, 1, 1, 1),
            size_hint_x=0.5,
            halign='right'
        )
        deposit_label.bind(size=deposit_label.setter('text_size'))
        
        finance_layout.add_widget(rent_label)
        finance_layout.add_widget(deposit_label)
        self.add_widget(finance_layout)
        
        # Electricity info
        electricity_layout = BoxLayout(size_hint_y=None, height=dp(25))
        electricity_label = Label(
            text=f"⚡ {int(self.room_data.get('electricity_fee', 0))} kWh",
            font_size=dp(12),
            color=(1, 1, 1, 1),
            halign='left'
        )
        electricity_label.bind(size=electricity_label.setter('text_size'))
        electricity_layout.add_widget(electricity_label)
        self.add_widget(electricity_layout)
        
        # Tenant info (if rented)
        if self.room_data["status"] == "已出租" and self.room_data.get("tenant_ids"):
            tenant_layout = BoxLayout(size_hint_y=None, height=dp(25))
            # Get tenant names (simplified for now)
            tenant_names = "租户信息"  # Will be populated from tenant data
            tenant_label = Label(
                text=f"👤 {tenant_names}",
                font_size=dp(12),
                color=(1, 1, 1, 1),
                halign='left'
            )
            tenant_label.bind(size=tenant_label.setter('text_size'))
            tenant_layout.add_widget(tenant_label)
            self.add_widget(tenant_layout)
        
        # Add touch event
        self.bind(on_touch_down=self.on_card_touch)
    
    def on_card_touch(self, instance, touch):
        """Handle card touch events"""
        if self.collide_point(*touch.pos):
            # Open room details screen
            self.app_instance.open_room_details(self.room_data)
            return True
        return False

class MainScreen(Screen):
    """Main dashboard screen"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = 'main'
        self.build_ui()
    
    def build_ui(self):
        """Build the main screen UI"""
        main_layout = BoxLayout(orientation='vertical')
        
        # Header
        header = self.create_header()
        main_layout.add_widget(header)
        
        # Financial summary (collapsible)
        summary = self.create_financial_summary()
        main_layout.add_widget(summary)
        
        # Search bar
        search_bar = self.create_search_bar()
        main_layout.add_widget(search_bar)
        
        # Room cards scroll view
        self.rooms_scroll = self.create_rooms_scroll()
        main_layout.add_widget(self.rooms_scroll)
        
        # Bottom navigation
        bottom_nav = self.create_bottom_navigation()
        main_layout.add_widget(bottom_nav)
        
        self.add_widget(main_layout)
    
    def create_header(self):
        """Create the header with title and settings"""
        header = BoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(56),
            padding=dp(16),
            spacing=dp(16)
        )
        
        # Menu button (hamburger)
        menu_btn = Button(
            text="≡",
            font_size=dp(20),
            size_hint_x=None,
            width=dp(40),
            background_color=get_color_from_hex('#3b82f6')
        )
        
        # Title
        title = Label(
            text="房间管理系统",
            font_size=dp(20),
            bold=True,
            color=get_color_from_hex('#1e293b')
        )
        
        # Settings button
        settings_btn = Button(
            text="⚙️",
            font_size=dp(16),
            size_hint_x=None,
            width=dp(40),
            background_color=get_color_from_hex('#64748b')
        )
        
        header.add_widget(menu_btn)
        header.add_widget(title)
        header.add_widget(settings_btn)
        
        return header
    
    def create_financial_summary(self):
        """Create financial summary card"""
        summary_layout = BoxLayout(
            orientation='vertical',
            size_hint_y=None,
            height=dp(120),
            padding=dp(16),
            spacing=dp(8)
        )
        
        # Summary card background
        with summary_layout.canvas.before:
            from kivy.graphics import Color, RoundedRectangle
            Color(*get_color_from_hex('#ffffff'))
            summary_layout.rect = RoundedRectangle(
                pos=summary_layout.pos,
                size=summary_layout.size,
                radius=[dp(8)]
            )
        
        summary_layout.bind(pos=self.update_summary_rect, size=self.update_summary_rect)
        
        # Title
        title = Label(
            text="📊 财务概览",
            font_size=dp(16),
            bold=True,
            color=get_color_from_hex('#1e293b'),
            size_hint_y=None,
            height=dp(30)
        )
        
        # Financial info grid
        finance_grid = GridLayout(cols=3, spacing=dp(8))
        
        # Will be populated with actual data
        rent_label = Label(text="💰 租金总额\n¥0", font_size=dp(12), halign='center')
        deposit_label = Label(text="🏦 押金总额\n¥0", font_size=dp(12), halign='center')
        electricity_label = Label(text="⚡ 总电量\n0 kWh", font_size=dp(12), halign='center')
        
        for label in [rent_label, deposit_label, electricity_label]:
            label.bind(size=label.setter('text_size'))
            label.color = get_color_from_hex('#64748b')
        
        finance_grid.add_widget(rent_label)
        finance_grid.add_widget(deposit_label)
        finance_grid.add_widget(electricity_label)
        
        summary_layout.add_widget(title)
        summary_layout.add_widget(finance_grid)
        
        return summary_layout
    
    def update_summary_rect(self, instance, *args):
        """Update summary card background"""
        instance.rect.pos = instance.pos
        instance.rect.size = instance.size
    
    def create_search_bar(self):
        """Create search bar"""
        search_layout = BoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(48),
            padding=dp(16),
            spacing=dp(8)
        )
        
        search_input = TextInput(
            hint_text="🔍 搜索房间...",
            font_size=dp(14),
            multiline=False,
            background_color=get_color_from_hex('#f1f5f9')
        )
        
        search_layout.add_widget(search_input)
        return search_layout
    
    def create_rooms_scroll(self):
        """Create scrollable room cards area"""
        scroll = ScrollView()
        
        # Grid layout for room cards (2 columns on mobile)
        self.rooms_grid = GridLayout(
            cols=2,
            spacing=dp(8),
            padding=dp(16),
            size_hint_y=None
        )
        self.rooms_grid.bind(minimum_height=self.rooms_grid.setter('height'))
        
        scroll.add_widget(self.rooms_grid)
        return scroll
    
    def create_bottom_navigation(self):
        """Create bottom navigation bar"""
        nav_layout = BoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(56),
            spacing=dp(1)
        )
        
        # Navigation buttons
        rooms_btn = Button(
            text="🏠\n房间",
            font_size=dp(12),
            background_color=get_color_from_hex('#3b82f6')
        )
        
        tenants_btn = Button(
            text="👥\n租户",
            font_size=dp(12),
            background_color=get_color_from_hex('#64748b')
        )
        
        reports_btn = Button(
            text="📊\n报表",
            font_size=dp(12),
            background_color=get_color_from_hex('#64748b')
        )
        
        nav_layout.add_widget(rooms_btn)
        nav_layout.add_widget(tenants_btn)
        nav_layout.add_widget(reports_btn)
        
        return nav_layout
    
    def refresh_rooms(self, rooms_data):
        """Refresh room cards with new data"""
        self.rooms_grid.clear_widgets()
        
        for room in rooms_data:
            room_card = RoomCard(room, self.manager.app)
            self.rooms_grid.add_widget(room_card)

class RoomManagementApp(App):
    """Main application class"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.data_manager = DataManager()
        self.rooms = []
        self.persons = []
        self.financial_data = {}
        
    def build(self):
        """Build the application"""
        # Load data
        self.load_data()
        
        # Create screen manager
        sm = ScreenManager()
        
        # Add main screen
        main_screen = MainScreen()
        sm.add_widget(main_screen)
        
        # Store reference for easy access
        sm.app = self
        
        # Refresh rooms display
        Clock.schedule_once(lambda dt: main_screen.refresh_rooms(self.rooms), 0.1)
        
        return sm
    
    def load_data(self):
        """Load data using data manager"""
        try:
            self.rooms = self.data_manager.get_all_rooms()
            self.persons = self.data_manager.get_all_persons()
            self.financial_data = self.data_manager.get_financial_data()
        except Exception as e:
            print(f"Error loading data: {e}")

    def save_data(self):
        """Save data using data manager"""
        try:
            # Financial data is saved separately when updated
            self.data_manager.save_financial_data(self.financial_data)
        except Exception as e:
            print(f"Error saving data: {e}")
    
    def open_room_details(self, room_data):
        """Open room details screen"""
        # Create room details screen if it doesn't exist
        details_screen_name = f"room_details_{room_data['id']}"

        # Check if screen already exists
        if not self.root.has_screen(details_screen_name):
            details_screen = RoomDetailsScreen(room_data, self)
            details_screen.name = details_screen_name
            self.root.add_widget(details_screen)

        # Switch to room details screen
        self.root.current = details_screen_name

if __name__ == '__main__':
    RoomManagementApp().run()
