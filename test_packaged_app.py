#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包后程序功能测试脚本
"""

import os
import sys
import subprocess
import time
import json
from pathlib import Path

def test_executable_exists():
    """测试可执行文件是否存在"""
    exe_path = Path("release/房间管理系统.exe")
    if exe_path.exists():
        size_mb = exe_path.stat().st_size / (1024 * 1024)
        print(f"✅ 可执行文件存在: {exe_path}")
        print(f"📁 文件大小: {size_mb:.1f} MB")
        return True
    else:
        print(f"❌ 可执行文件不存在: {exe_path}")
        return False

def test_data_files():
    """测试数据文件是否存在"""
    data_files = [
        "release/rooms.json",
        "release/persons.json", 
        "release/financial_data.json"
    ]
    
    all_exist = True
    for file_path in data_files:
        if os.path.exists(file_path):
            print(f"✅ 数据文件存在: {file_path}")
            # 验证JSON格式
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    json.load(f)
                print(f"   📄 JSON格式正确")
            except json.JSONDecodeError as e:
                print(f"   ❌ JSON格式错误: {e}")
                all_exist = False
        else:
            print(f"❌ 数据文件不存在: {file_path}")
            all_exist = False
    
    return all_exist

def test_readme_file():
    """测试使用说明文件"""
    readme_path = "release/使用说明.txt"
    if os.path.exists(readme_path):
        print(f"✅ 使用说明存在: {readme_path}")
        with open(readme_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if "房间管理系统" in content and "使用说明" in content:
                print("   📄 使用说明内容正确")
                return True
            else:
                print("   ❌ 使用说明内容不完整")
                return False
    else:
        print(f"❌ 使用说明不存在: {readme_path}")
        return False

def test_app_launch():
    """测试程序启动（非阻塞）"""
    exe_path = "release/房间管理系统.exe"
    if not os.path.exists(exe_path):
        print("❌ 无法测试启动：可执行文件不存在")
        return False
    
    try:
        print("🚀 尝试启动程序...")
        # 使用非阻塞方式启动程序
        process = subprocess.Popen([exe_path], 
                                 cwd="release",
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        # 等待一小段时间检查程序是否正常启动
        time.sleep(3)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ 程序启动成功，正在运行中")
            print("💡 请手动关闭程序窗口以继续测试")
            
            # 等待用户关闭程序
            try:
                process.wait(timeout=30)  # 等待最多30秒
                print("✅ 程序已正常关闭")
            except subprocess.TimeoutExpired:
                print("⏰ 程序仍在运行，强制结束测试")
                process.terminate()
            
            return True
        else:
            # 程序已退出，检查退出代码
            return_code = process.returncode
            stdout, stderr = process.communicate()
            
            if return_code == 0:
                print("✅ 程序启动并正常退出")
                return True
            else:
                print(f"❌ 程序启动失败，退出代码: {return_code}")
                if stderr:
                    print(f"错误信息: {stderr.decode('utf-8', errors='ignore')}")
                return False
                
    except Exception as e:
        print(f"❌ 启动测试失败: {e}")
        return False

def test_file_permissions():
    """测试文件权限"""
    exe_path = "release/房间管理系统.exe"
    if os.path.exists(exe_path):
        if os.access(exe_path, os.X_OK):
            print("✅ 可执行文件有执行权限")
            return True
        else:
            print("❌ 可执行文件没有执行权限")
            return False
    return False

def generate_test_report():
    """生成测试报告"""
    report_content = f"""# 房间管理系统打包测试报告

## 测试时间
{time.strftime('%Y-%m-%d %H:%M:%S')}

## 测试环境
- 操作系统: {os.name}
- Python版本: {sys.version}
- 工作目录: {os.getcwd()}

## 测试结果

### 文件完整性测试
"""
    
    # 执行各项测试
    tests = [
        ("可执行文件存在性", test_executable_exists),
        ("数据文件完整性", test_data_files),
        ("使用说明文件", test_readme_file),
        ("文件权限检查", test_file_permissions),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 执行测试: {test_name}")
        result = test_func()
        results.append((test_name, result))
        report_content += f"- {test_name}: {'✅ 通过' if result else '❌ 失败'}\n"
    
    # 程序启动测试
    print(f"\n🧪 执行测试: 程序启动测试")
    launch_result = test_app_launch()
    results.append(("程序启动测试", launch_result))
    report_content += f"- 程序启动测试: {'✅ 通过' if launch_result else '❌ 失败'}\n"
    
    # 统计结果
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    report_content += f"""
## 测试总结
- 总测试项: {total}
- 通过项: {passed}
- 失败项: {total - passed}
- 通过率: {passed/total*100:.1f}%

## 结论
{'✅ 所有测试通过，程序打包成功！' if passed == total else '❌ 部分测试失败，需要检查问题'}
"""
    
    # 保存测试报告
    with open("打包测试报告.md", "w", encoding="utf-8") as f:
        f.write(report_content)
    
    print(f"\n📊 测试完成!")
    print(f"📁 测试报告已保存: 打包测试报告.md")
    print(f"📈 测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    return passed == total

def main():
    """主函数"""
    print("🧪 房间管理系统打包测试工具")
    print("=" * 50)
    
    # 检查release目录是否存在
    if not os.path.exists("release"):
        print("❌ release目录不存在，请先执行打包")
        return False
    
    # 执行测试并生成报告
    success = generate_test_report()
    
    if success:
        print("\n🎉 所有测试通过！打包成功！")
        print("💡 可以将release目录分发给用户使用")
    else:
        print("\n⚠️ 部分测试失败，请检查问题")
    
    return success

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
