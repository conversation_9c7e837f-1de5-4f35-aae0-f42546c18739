#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试标准化对话框的功能和视觉效果
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加当前目录到路径，以便导入房间管理系统的类
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入标准化对话框类
from 房间管理系统2 import StandardDialog, StandardMessageBox

class DialogTester:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🧪 标准化对话框测试工具")
        self.root.geometry("600x500")
        self.root.configure(bg="#f8fafc")
        
        # 使用与房间管理系统相同的配色方案
        self.colors = {
            'primary': '#1e3a8a',
            'primary_light': '#3b82f6',
            'secondary': '#059669',
            'secondary_light': '#10b981',
            'warning': '#d97706',
            'warning_light': '#f59e0b',
            'danger': '#dc2626',
            'danger_light': '#ef4444',
            'info': '#0891b2',
            'info_light': '#06b6d4',
            'background': '#f8fafc',
            'card_bg': '#ffffff',
            'text_primary': '#1e293b',
            'text_secondary': '#64748b',
            'border': '#e2e8f0',
            'white': '#ffffff',
            'medium_gray': '#e2e8f0',
            'dark_gray': '#64748b'
        }
        
        self.fonts = {
            'title': ('Microsoft YaHei UI', 24, 'bold'),
            'heading': ('Microsoft YaHei UI', 16, 'bold'),
            'subheading': ('Microsoft YaHei UI', 14, 'bold'),
            'body': ('Microsoft YaHei UI', 11),
            'body_bold': ('Microsoft YaHei UI', 11, 'bold'),
            'button': ('Microsoft YaHei UI', 10, 'bold'),
            'caption': ('Microsoft YaHei UI', 9)
        }
        
        # 初始化标准化消息框
        self.msgbox = StandardMessageBox(self.root, self.colors, self.fonts)
        
        self.create_interface()
        
    def create_interface(self):
        """创建测试界面"""
        # 主标题
        title_label = tk.Label(self.root,
                             text="🧪 标准化对话框测试工具",
                             font=self.fonts['title'],
                             bg=self.colors['background'],
                             fg=self.colors['primary'])
        title_label.pack(pady=20)
        
        # 说明文字
        desc_label = tk.Label(self.root,
                            text="点击下方按钮测试不同类型的标准化对话框",
                            font=self.fonts['body'],
                            bg=self.colors['background'],
                            fg=self.colors['text_secondary'])
        desc_label.pack(pady=(0, 30))
        
        # 按钮容器
        button_frame = tk.Frame(self.root, bg=self.colors['background'])
        button_frame.pack(expand=True, fill="both", padx=50, pady=20)
        
        # 测试按钮
        buttons = [
            ("✅ 测试成功对话框", self.test_success, self.colors['secondary']),
            ("ℹ️ 测试信息对话框", self.test_info, self.colors['info']),
            ("⚠️ 测试警告对话框", self.test_warning, self.colors['warning']),
            ("❌ 测试错误对话框", self.test_error, self.colors['danger']),
            ("❓ 测试确认对话框", self.test_confirm, self.colors['primary']),
            ("🔄 测试复杂消息", self.test_complex, self.colors['medium_gray']),
            ("📏 测试长文本显示", self.test_long_text, self.colors['info']),
            ("📋 测试多行内容", self.test_multiline, self.colors['secondary']),
            ("🔘 测试按钮显示", self.test_button_display, self.colors['primary'])
        ]
        
        for i, (text, command, color) in enumerate(buttons):
            btn = tk.Button(button_frame,
                          text=text,
                          font=self.fonts['button'],
                          bg=color,
                          fg=self.colors['white'],
                          relief="flat",
                          bd=0,
                          padx=20,
                          pady=12,
                          command=command,
                          cursor="hand2",
                          activebackground=color,
                          activeforeground=self.colors['white'])
            btn.pack(pady=8, fill="x")
            
            # 添加悬停效果
            def on_enter(e, btn=btn, hover_color=color):
                btn.configure(bg=hover_color)
            def on_leave(e, btn=btn, orig_color=color):
                btn.configure(bg=orig_color)
            
            btn.bind("<Enter>", on_enter)
            btn.bind("<Leave>", on_leave)
        
        # 退出按钮
        exit_btn = tk.Button(self.root,
                           text="🚪 退出测试",
                           font=self.fonts['button'],
                           bg=self.colors['dark_gray'],
                           fg=self.colors['white'],
                           relief="flat",
                           bd=0,
                           padx=20,
                           pady=8,
                           command=self.root.quit,
                           cursor="hand2")
        exit_btn.pack(pady=20)
    
    def test_success(self):
        """测试成功对话框"""
        self.msgbox.showsuccess("✅ 操作成功", 
                               "房间添加成功！\n\n房间号：101\n租金：¥1500\n押金：¥3000")
    
    def test_info(self):
        """测试信息对话框"""
        self.msgbox.showinfo("ℹ️ 系统信息", 
                            "当前系统版本：v2.0\n\n功能特性：\n• 现代化界面设计\n• 统一的对话框样式\n• 完整的租赁管理功能")
    
    def test_warning(self):
        """测试警告对话框"""
        self.msgbox.showwarning("⚠️ 操作警告", 
                               "该房间已有租户入住！\n\n请先办理退房手续后再进行其他操作。")
    
    def test_error(self):
        """测试错误对话框"""
        self.msgbox.showerror("❌ 操作失败", 
                             "数据保存失败！\n\n错误原因：文件权限不足\n请检查文件夹权限设置。")
    
    def test_confirm(self):
        """测试确认对话框"""
        result = self.msgbox.askyesno("❓ 确认操作", 
                                     "确定要删除房间 101 吗？\n\n此操作不可撤销！")
        if result:
            self.msgbox.showsuccess("✅ 确认结果", "您选择了：确定")
        else:
            self.msgbox.showinfo("ℹ️ 确认结果", "您选择了：取消")
    
    def test_complex(self):
        """测试复杂消息内容"""
        complex_message = """租房办理成功！

📋 租赁详情：
• 房间号：101
• 租户：张三
• 租期：1年
• 起租日期：2024-01-15
• 截止日期：2025-01-14

💰 费用明细：
• 收取租金：¥18000
• 收取押金：¥3000
• 收取电费：¥200

📊 累计统计：
• 累计租金总额：¥156000
• 累计押金总额：¥45000"""
        
        self.msgbox.showsuccess("✅ 租房成功", complex_message)

    def test_long_text(self):
        """测试长文本显示"""
        long_message = """这是一个非常长的文本消息，用来测试对话框是否能够正确处理和显示长文本内容。这个消息包含了很多文字，目的是验证文本换行、窗口尺寸调整和内容完整显示的功能。在实际的房间管理系统中，可能会遇到各种长度的消息内容，比如详细的操作说明、错误信息描述、或者包含多项数据的成功提示。因此，确保对话框能够优雅地处理这些长文本内容是非常重要的。这个测试用例就是为了验证这一点而设计的。"""

        self.msgbox.showinfo("📏 长文本测试", long_message)

    def test_multiline(self):
        """测试多行内容显示"""
        multiline_message = """多行内容测试：

第一行：基本信息
第二行：详细描述
第三行：操作结果

列表内容：
• 项目一：成功完成
• 项目二：正在处理
• 项目三：等待开始

数据统计：
- 总计房间：25间
- 已出租：18间
- 空闲房间：7间

注意事项：
1. 请确保数据准确性
2. 定期备份重要信息
3. 及时更新系统状态

这是一个包含多种格式的多行文本测试，用来验证对话框的文本显示能力。"""

        self.msgbox.showwarning("📋 多行内容测试", multiline_message)

    def test_button_display(self):
        """测试按钮显示效果"""
        # 测试单按钮对话框
        self.msgbox.showsuccess("🔘 单按钮测试",
                               "这是单按钮对话框测试。\n\n请检查：\n• 按钮是否完整显示\n• 按钮尺寸是否合适\n• 按钮位置是否正确")

        # 测试双按钮对话框
        result = self.msgbox.askyesno("🔘 双按钮测试",
                                     "这是双按钮对话框测试。\n\n请检查：\n• 两个按钮是否都完整显示\n• 按钮间距是否合理\n• 按钮尺寸是否一致\n\n点击任意按钮继续测试")

        # 根据用户选择显示结果
        if result:
            self.msgbox.showinfo("✅ 测试结果", "您点击了【确定】按钮\n\n按钮显示测试完成！")
        else:
            self.msgbox.showinfo("ℹ️ 测试结果", "您点击了【取消】按钮\n\n按钮显示测试完成！")

    def run(self):
        """运行测试程序"""
        self.root.mainloop()

if __name__ == "__main__":
    tester = DialogTester()
    tester.run()
