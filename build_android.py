#!/usr/bin/env python3
"""
Android Build Script for Room Management System
Automates the build process using Buildozer
"""

import os
import sys
import subprocess
import shutil
import argparse
from pathlib import Path

class AndroidBuilder:
    """Handles Android build process"""
    
    def __init__(self):
        self.project_dir = Path(__file__).parent
        self.buildozer_spec = self.project_dir / "buildozer.spec"
        self.requirements_file = self.project_dir / "requirements_mobile.txt"
        
    def check_prerequisites(self):
        """Check if all prerequisites are installed"""
        print("🔍 Checking prerequisites...")
        
        # Check Python version
        if sys.version_info < (3, 8):
            print("❌ Python 3.8+ is required")
            return False
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
        
        # Check if buildozer is installed
        try:
            result = subprocess.run(['buildozer', 'version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ Buildozer is installed")
            else:
                print("❌ Buildozer is not working properly")
                return False
        except FileNotFoundError:
            print("❌ Buildozer is not installed")
            print("   Install with: pip install buildozer")
            return False
        
        # Check if buildozer.spec exists
        if not self.buildozer_spec.exists():
            print("❌ buildozer.spec not found")
            return False
        print("✅ buildozer.spec found")
        
        # Check if main.py exists
        if not (self.project_dir / "main.py").exists():
            print("❌ main.py not found")
            return False
        print("✅ main.py found")
        
        return True
    
    def setup_environment(self):
        """Setup build environment"""
        print("🔧 Setting up build environment...")
        
        # Create necessary directories
        data_dir = self.project_dir / "data"
        if not data_dir.exists():
            data_dir.mkdir()
            print("✅ Created data directory")
        
        # Copy sample data files if they exist
        for data_file in ["rooms.json", "persons.json", "financial_data.json"]:
            src_file = self.project_dir / data_file
            dst_file = data_dir / data_file
            if src_file.exists() and not dst_file.exists():
                shutil.copy2(src_file, dst_file)
                print(f"✅ Copied {data_file} to data directory")
        
        print("✅ Environment setup complete")
    
    def clean_build(self):
        """Clean previous build artifacts"""
        print("🧹 Cleaning previous build...")
        
        # Remove buildozer directories
        for dir_name in [".buildozer", "bin"]:
            dir_path = self.project_dir / dir_name
            if dir_path.exists():
                shutil.rmtree(dir_path)
                print(f"✅ Removed {dir_name}")
        
        print("✅ Clean complete")
    
    def build_debug(self):
        """Build debug APK"""
        print("🔨 Building debug APK...")
        
        try:
            # Change to project directory
            os.chdir(self.project_dir)
            
            # Run buildozer android debug
            result = subprocess.run(['buildozer', 'android', 'debug'], 
                                  check=True)
            
            print("✅ Debug build completed successfully!")
            
            # Find the generated APK
            bin_dir = self.project_dir / "bin"
            if bin_dir.exists():
                apk_files = list(bin_dir.glob("*.apk"))
                if apk_files:
                    apk_file = apk_files[0]
                    print(f"📱 APK generated: {apk_file}")
                    print(f"   Size: {apk_file.stat().st_size / (1024*1024):.1f} MB")
                    return str(apk_file)
            
            return None
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Build failed with error code {e.returncode}")
            return None
        except Exception as e:
            print(f"❌ Build failed: {e}")
            return None
    
    def build_release(self):
        """Build release APK"""
        print("🔨 Building release APK...")
        
        try:
            # Change to project directory
            os.chdir(self.project_dir)
            
            # Run buildozer android release
            result = subprocess.run(['buildozer', 'android', 'release'], 
                                  check=True)
            
            print("✅ Release build completed successfully!")
            
            # Find the generated APK
            bin_dir = self.project_dir / "bin"
            if bin_dir.exists():
                apk_files = list(bin_dir.glob("*-release*.apk"))
                if apk_files:
                    apk_file = apk_files[0]
                    print(f"📱 Release APK generated: {apk_file}")
                    print(f"   Size: {apk_file.stat().st_size / (1024*1024):.1f} MB")
                    return str(apk_file)
            
            return None
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Release build failed with error code {e.returncode}")
            return None
        except Exception as e:
            print(f"❌ Release build failed: {e}")
            return None
    
    def install_debug(self, device_id=None):
        """Install debug APK to connected device"""
        print("📱 Installing debug APK...")
        
        try:
            cmd = ['buildozer', 'android', 'deploy']
            if device_id:
                cmd.extend(['--device', device_id])
            
            subprocess.run(cmd, check=True)
            print("✅ APK installed successfully!")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Installation failed with error code {e.returncode}")
            return False
        except Exception as e:
            print(f"❌ Installation failed: {e}")
            return False
    
    def run_on_device(self, device_id=None):
        """Run the app on connected device"""
        print("🚀 Running app on device...")
        
        try:
            cmd = ['buildozer', 'android', 'run']
            if device_id:
                cmd.extend(['--device', device_id])
            
            subprocess.run(cmd, check=True)
            print("✅ App started successfully!")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to run app with error code {e.returncode}")
            return False
        except Exception as e:
            print(f"❌ Failed to run app: {e}")
            return False
    
    def show_devices(self):
        """Show connected Android devices"""
        print("📱 Connected Android devices:")
        
        try:
            result = subprocess.run(['adb', 'devices'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                devices = [line.split('\t')[0] for line in lines if '\tdevice' in line]
                
                if devices:
                    for i, device in enumerate(devices):
                        print(f"  {i+1}. {device}")
                    return devices
                else:
                    print("  No devices connected")
                    return []
            else:
                print("  Unable to list devices (adb not found or not working)")
                return []
                
        except FileNotFoundError:
            print("  ADB not found. Install Android SDK platform-tools")
            return []

def main():
    """Main build script entry point"""
    parser = argparse.ArgumentParser(description='Build Android APK for Room Management System')
    parser.add_argument('action', choices=['debug', 'release', 'clean', 'install', 'run', 'devices'],
                       help='Build action to perform')
    parser.add_argument('--device', '-d', help='Device ID for install/run actions')
    parser.add_argument('--clean-first', action='store_true', 
                       help='Clean before building')
    
    args = parser.parse_args()
    
    builder = AndroidBuilder()
    
    print("🏠 Room Management System - Android Builder")
    print("=" * 50)
    
    # Check prerequisites
    if not builder.check_prerequisites():
        print("\n❌ Prerequisites check failed. Please fix the issues above.")
        sys.exit(1)
    
    # Handle different actions
    if args.action == 'clean':
        builder.clean_build()
        
    elif args.action == 'devices':
        builder.show_devices()
        
    elif args.action in ['debug', 'release']:
        # Clean first if requested
        if args.clean_first:
            builder.clean_build()
        
        # Setup environment
        builder.setup_environment()
        
        # Build
        if args.action == 'debug':
            apk_file = builder.build_debug()
        else:
            apk_file = builder.build_release()
        
        if apk_file:
            print(f"\n🎉 Build successful!")
            print(f"APK location: {apk_file}")
            print("\nNext steps:")
            print("1. Transfer APK to your Android device")
            print("2. Enable 'Unknown sources' in Android settings")
            print("3. Install the APK")
            print("4. Or use: python build_android.py install")
        else:
            print("\n❌ Build failed!")
            sys.exit(1)
    
    elif args.action == 'install':
        if not builder.install_debug(args.device):
            sys.exit(1)
    
    elif args.action == 'run':
        if not builder.run_on_device(args.device):
            sys.exit(1)

if __name__ == '__main__':
    main()
