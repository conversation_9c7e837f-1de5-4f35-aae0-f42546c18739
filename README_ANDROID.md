# 🏠 Room Management System - Android Version

A modern, mobile-optimized room management application built with Python and Kivy for Android devices.

## 📱 Features

### Core Functionality
- **Room Management**: Add, edit, and delete rooms with detailed information
- **Tenant Management**: Track tenant information and rental history
- **Financial Tracking**: Monitor rent collection, deposits, and electricity usage
- **Rental Periods**: Support for monthly, quarterly, semi-annual, and annual rentals
- **Status Monitoring**: Real-time tracking of room availability and lease expiration
- **Data Persistence**: Secure local storage with JSON-based data management

### Mobile-Optimized UI
- **Touch-First Design**: Optimized for finger navigation and touch interactions
- **Responsive Layout**: Adapts to different screen sizes (phones and tablets)
- **Modern Interface**: Card-based design with intuitive navigation
- **Visual Status Indicators**: Color-coded room status with emoji icons
- **Bottom Navigation**: Easy thumb-accessible navigation bar
- **Swipe Gestures**: Natural mobile interaction patterns

### Technical Features
- **Cross-Platform**: Runs on Android 5.0+ (API level 21+)
- **Offline Operation**: No internet connection required
- **Data Security**: Local data storage with backup capabilities
- **Performance Optimized**: Efficient memory usage and smooth animations
- **Accessibility**: Support for screen readers and accessibility features

## 🚀 Quick Start

### For End Users

1. **Download APK**: Get the latest APK from releases
2. **Enable Installation**: Allow installation from unknown sources in Android settings
3. **Install**: Tap the APK file and follow installation prompts
4. **Launch**: Open the app and start managing your rooms

### For Developers

1. **Setup Environment**:
   ```bash
   python setup_build_environment.py
   ```

2. **Build APK**:
   ```bash
   python build_android.py debug
   ```

3. **Install on Device**:
   ```bash
   python build_android.py install
   ```

## 📋 System Requirements

### For End Users
- **Android**: 5.0 (API level 21) or higher
- **RAM**: 2GB minimum (4GB recommended)
- **Storage**: 100MB free space
- **Permissions**: Storage access for data files

### For Developers
- **Python**: 3.8 or higher
- **Java**: OpenJDK 11 or higher
- **Android SDK**: API level 21+
- **RAM**: 8GB minimum (16GB recommended)
- **Storage**: 10GB free space for build tools

## 🛠️ Installation Guide

### Method 1: Direct APK Installation

1. **Download** the APK file from the releases section
2. **Enable Unknown Sources**:
   - Go to Settings > Security
   - Enable "Unknown sources" or "Install unknown apps"
3. **Install**:
   - Open file manager and locate the APK
   - Tap the APK file and select "Install"
   - Wait for installation to complete

### Method 2: Build from Source

1. **Clone Repository**:
   ```bash
   git clone <repository-url>
   cd room-management-android
   ```

2. **Setup Environment**:
   ```bash
   python setup_build_environment.py
   ```

3. **Build APK**:
   ```bash
   python build_android.py debug
   ```

4. **Install**:
   ```bash
   python build_android.py install
   ```

## 📖 User Guide

### Getting Started

1. **First Launch**: The app will create sample data for demonstration
2. **Main Dashboard**: View all rooms in a card-based layout
3. **Room Details**: Tap any room card to view detailed information
4. **Navigation**: Use bottom navigation to switch between sections

### Managing Rooms

#### Adding a New Room
1. Tap the "+" floating action button
2. Fill in room details (number, rent, deposit, electricity)
3. Add optional notes
4. Tap "Save" to create the room

#### Editing Room Information
1. Tap on a room card to open details
2. Tap "Edit" button
3. Modify the information as needed
4. Save changes

#### Renting Out a Room
1. Open room details for an available room
2. Tap "Rent Out" button
3. Select or add tenant information
4. Set rental period and dates
5. Confirm rental agreement

### Financial Management

#### Tracking Finances
- **Dashboard Overview**: View total rent and deposit collected
- **Individual Room**: See financial details for each room
- **Electricity Tracking**: Monitor electricity consumption per room

#### Collecting Rent
1. Open rented room details
2. Tap "Extend Lease" for rent collection
3. Select new rental period
4. System automatically updates financial totals

### Data Management

#### Backup Data
- Data is automatically saved locally
- Use the backup feature in settings for additional security
- Export data for external backup if needed

#### Restore Data
- Import previously exported data
- Restore from backup files
- Migrate data from desktop version

## 🔧 Development

### Project Structure
```
room-management-android/
├── main.py                 # Main application entry point
├── room_details_screen.py  # Room details screen implementation
├── data_manager.py         # Data persistence layer
├── buildozer.spec         # Android build configuration
├── requirements_mobile.txt # Python dependencies
├── build_android.py       # Build automation script
├── setup_build_environment.py # Environment setup
├── testing_guide.md       # Testing documentation
├── deployment_guide.md    # Deployment instructions
└── mobile_ui_design_spec.md # UI design specifications
```

### Key Components

#### Main Application (`main.py`)
- Application entry point
- Screen management
- Navigation handling
- Data loading and saving

#### Room Details Screen (`room_details_screen.py`)
- Detailed room information display
- Room editing functionality
- Rental management features

#### Data Manager (`data_manager.py`)
- Android-compatible data storage
- JSON-based persistence
- Backup and restore functionality

### Building and Testing

#### Development Build
```bash
# Build debug APK
python build_android.py debug

# Install on connected device
python build_android.py install

# Run directly on device
python build_android.py run
```

#### Release Build
```bash
# Build release APK
python build_android.py release

# Sign APK for distribution
# (See deployment guide for signing instructions)
```

#### Testing
```bash
# Run unit tests
python -m pytest tests/

# Test on emulator
python build_android.py install

# View logs
adb logcat | grep python
```

## 📚 Documentation

- **[Mobile UI Design Specification](mobile_ui_design_spec.md)**: Detailed UI/UX design guidelines
- **[Testing Guide](testing_guide.md)**: Comprehensive testing procedures
- **[Deployment Guide](deployment_guide.md)**: Step-by-step deployment instructions
- **[Build Configuration](buildozer.spec)**: Android build settings

## 🤝 Contributing

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

### Development Guidelines
- Follow Python PEP 8 style guidelines
- Write unit tests for new features
- Update documentation for changes
- Test on multiple Android versions
- Ensure accessibility compliance

## 🐛 Troubleshooting

### Common Issues

#### App Won't Install
- Enable "Unknown sources" in Android settings
- Check available storage space
- Verify Android version compatibility (5.0+)

#### App Crashes on Startup
- Check device logs: `adb logcat | grep python`
- Verify all dependencies are included
- Test on different Android versions

#### Data Not Saving
- Check storage permissions
- Verify external storage availability
- Test with different file locations

#### Build Failures
- Verify Java installation: `java -version`
- Check Android SDK path: `echo $ANDROID_HOME`
- Update buildozer: `pip install --upgrade buildozer`

### Getting Help
- Check the [Issues](../../issues) section for known problems
- Review the [Testing Guide](testing_guide.md) for debugging steps
- Create a new issue with detailed error information

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Kivy Team**: For the excellent cross-platform framework
- **Python Community**: For the robust ecosystem
- **Android Developers**: For comprehensive documentation
- **Contributors**: Everyone who helped improve this project

## 📞 Support

For support and questions:
- **Issues**: Use GitHub Issues for bug reports and feature requests
- **Documentation**: Check the guides in the `docs/` folder
- **Community**: Join discussions in the project's community section

---

**Made with ❤️ using Python and Kivy**
