#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的按钮显示测试脚本
"""

import tkinter as tk
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入标准化对话框类
from 房间管理系统2 import StandardMessageBox

def simple_test():
    """简单测试按钮显示"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 配色方案
    colors = {
        'primary': '#1e3a8a',
        'primary_light': '#3b82f6',
        'secondary': '#059669',
        'secondary_light': '#10b981',
        'warning': '#d97706',
        'warning_light': '#f59e0b',
        'danger': '#dc2626',
        'danger_light': '#ef4444',
        'info': '#0891b2',
        'info_light': '#06b6d4',
        'background': '#f8fafc',
        'card_bg': '#ffffff',
        'text_primary': '#1e293b',
        'text_secondary': '#64748b',
        'border': '#e2e8f0',
        'white': '#ffffff',
        'medium_gray': '#e2e8f0',
        'dark_gray': '#64748b'
    }
    
    fonts = {
        'heading': ('Microsoft YaHei UI', 16, 'bold'),
        'body': ('Microsoft YaHei UI', 11),
        'button': ('Microsoft YaHei UI', 10, 'bold')
    }
    
    # 初始化消息框
    msgbox = StandardMessageBox(root, colors, fonts)
    
    print("🔘 开始简单按钮测试...")
    
    # 测试1：简单成功对话框
    print("📝 测试1：简单成功对话框")
    msgbox.showsuccess("测试", "这是一个简单的成功消息测试")
    
    # 测试2：简单确认对话框
    print("📝 测试2：简单确认对话框")
    result = msgbox.askyesno("确认", "这是一个简单的确认测试")
    print(f"   结果：{'确定' if result else '取消'}")
    
    # 测试3：错误对话框
    print("📝 测试3：错误对话框")
    msgbox.showerror("错误", "这是一个错误消息测试")
    
    print("✅ 简单测试完成！")
    root.destroy()

if __name__ == "__main__":
    simple_test()
