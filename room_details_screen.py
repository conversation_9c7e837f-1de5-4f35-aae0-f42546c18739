"""
Room Details Screen for Mobile Room Management System
"""

from kivy.uix.screenmanager import Screen
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.scrollview import ScrollView
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.uix.textinput import TextInput
from kivy.uix.popup import Popup
from kivy.metrics import dp
from kivy.utils import get_color_from_hex
from datetime import datetime, timedelta

class RoomDetailsScreen(Screen):
    """Room details and management screen"""
    
    def __init__(self, room_data, app_instance, **kwargs):
        super().__init__(**kwargs)
        self.name = 'room_details'
        self.room_data = room_data
        self.app_instance = app_instance
        self.build_ui()
    
    def build_ui(self):
        """Build the room details UI"""
        main_layout = BoxLayout(orientation='vertical')
        
        # Header with back button
        header = self.create_header()
        main_layout.add_widget(header)
        
        # Scrollable content
        scroll = ScrollView()
        content_layout = BoxLayout(
            orientation='vertical',
            size_hint_y=None,
            padding=dp(16),
            spacing=dp(16)
        )
        content_layout.bind(minimum_height=content_layout.setter('height'))
        
        # Room header info
        room_header = self.create_room_header()
        content_layout.add_widget(room_header)
        
        # Financial information section
        financial_section = self.create_financial_section()
        content_layout.add_widget(financial_section)
        
        # Tenant information section (if rented)
        if self.room_data["status"] == "已出租":
            tenant_section = self.create_tenant_section()
            content_layout.add_widget(tenant_section)
        
        # Rental period section (if rented)
        if self.room_data["status"] == "已出租":
            rental_section = self.create_rental_section()
            content_layout.add_widget(rental_section)
        
        # Notes section
        notes_section = self.create_notes_section()
        content_layout.add_widget(notes_section)
        
        scroll.add_widget(content_layout)
        main_layout.add_widget(scroll)
        
        # Action buttons
        actions = self.create_action_buttons()
        main_layout.add_widget(actions)
        
        self.add_widget(main_layout)
    
    def create_header(self):
        """Create header with back button and menu"""
        header = BoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(56),
            padding=dp(16),
            spacing=dp(16)
        )
        
        # Back button
        back_btn = Button(
            text="←",
            font_size=dp(20),
            size_hint_x=None,
            width=dp(40),
            background_color=get_color_from_hex('#64748b')
        )
        back_btn.bind(on_press=self.go_back)
        
        # Title
        title = Label(
            text=f"房间 {self.room_data['room_number']} 详情",
            font_size=dp(18),
            bold=True,
            color=get_color_from_hex('#1e293b')
        )
        
        # Menu button
        menu_btn = Button(
            text="⋮",
            font_size=dp(20),
            size_hint_x=None,
            width=dp(40),
            background_color=get_color_from_hex('#64748b')
        )
        
        header.add_widget(back_btn)
        header.add_widget(title)
        header.add_widget(menu_btn)
        
        return header
    
    def create_room_header(self):
        """Create room header with status"""
        header_layout = BoxLayout(
            orientation='vertical',
            size_hint_y=None,
            height=dp(80),
            padding=dp(16),
            spacing=dp(8)
        )
        
        # Set background color
        with header_layout.canvas.before:
            from kivy.graphics import Color, RoundedRectangle
            # Determine color based on status
            if self.room_data["status"] == "已出租":
                today = datetime.today().date()
                end_date = None
                if self.room_data.get("end_date"):
                    try:
                        end_date = datetime.strptime(self.room_data["end_date"], "%Y-%m-%d").date()
                    except ValueError:
                        pass
                
                if end_date and end_date < today:
                    bg_color = get_color_from_hex('#dc2626')  # Red for expired
                    status_text = "已到期 ⚠️"
                else:
                    bg_color = get_color_from_hex('#059669')  # Green for rented
                    status_text = "租赁中 ✅"
            else:
                bg_color = get_color_from_hex('#3b82f6')  # Blue for available
                status_text = "闲置 🏠"
            
            Color(*bg_color)
            header_layout.rect = RoundedRectangle(
                pos=header_layout.pos,
                size=header_layout.size,
                radius=[dp(8)]
            )
        
        header_layout.bind(pos=self.update_header_rect, size=self.update_header_rect)
        
        # Room number
        room_label = Label(
            text=f"🏠 房间 {self.room_data['room_number']}",
            font_size=dp(20),
            bold=True,
            color=(1, 1, 1, 1),
            size_hint_y=None,
            height=dp(40)
        )
        
        # Status
        status_label = Label(
            text=f"状态：{status_text}",
            font_size=dp(16),
            color=(1, 1, 1, 1),
            size_hint_y=None,
            height=dp(30)
        )
        
        header_layout.add_widget(room_label)
        header_layout.add_widget(status_label)
        
        return header_layout
    
    def update_header_rect(self, instance, *args):
        """Update header background rectangle"""
        instance.rect.pos = instance.pos
        instance.rect.size = instance.size
    
    def create_financial_section(self):
        """Create financial information section"""
        section = self.create_section("💰 财务信息")
        
        # Financial details grid
        finance_grid = GridLayout(cols=2, spacing=dp(8), size_hint_y=None, height=dp(120))
        
        # Rent
        rent_layout = BoxLayout(orientation='vertical', spacing=dp(4))
        rent_title = Label(text="月租金", font_size=dp(12), color=get_color_from_hex('#64748b'))
        rent_value = Label(text=f"¥{self.room_data['rent']:.0f}", font_size=dp(18), bold=True, color=get_color_from_hex('#1e293b'))
        rent_layout.add_widget(rent_title)
        rent_layout.add_widget(rent_value)
        
        # Deposit
        deposit_layout = BoxLayout(orientation='vertical', spacing=dp(4))
        deposit_title = Label(text="押金", font_size=dp(12), color=get_color_from_hex('#64748b'))
        deposit_value = Label(text=f"¥{self.room_data['deposit']:.0f}", font_size=dp(18), bold=True, color=get_color_from_hex('#1e293b'))
        deposit_layout.add_widget(deposit_title)
        deposit_layout.add_widget(deposit_value)
        
        # Electricity
        electricity_layout = BoxLayout(orientation='vertical', spacing=dp(4))
        electricity_title = Label(text="电表读数", font_size=dp(12), color=get_color_from_hex('#64748b'))
        electricity_value = Label(text=f"{int(self.room_data.get('electricity_fee', 0))} kWh", font_size=dp(18), bold=True, color=get_color_from_hex('#1e293b'))
        electricity_layout.add_widget(electricity_title)
        electricity_layout.add_widget(electricity_value)
        
        # Empty space for alignment
        empty_layout = BoxLayout()
        
        finance_grid.add_widget(rent_layout)
        finance_grid.add_widget(deposit_layout)
        finance_grid.add_widget(electricity_layout)
        finance_grid.add_widget(empty_layout)
        
        section.add_widget(finance_grid)
        return section
    
    def create_tenant_section(self):
        """Create tenant information section"""
        section = self.create_section("👥 租户信息")
        
        # Get tenant information
        tenant_info_layout = BoxLayout(orientation='vertical', spacing=dp(8), size_hint_y=None, height=dp(100))
        
        # For now, show placeholder - will be populated with actual tenant data
        if self.room_data.get("tenant_ids"):
            tenant_name = Label(
                text="姓名：张三",  # Placeholder
                font_size=dp(14),
                color=get_color_from_hex('#1e293b'),
                halign='left'
            )
            tenant_name.bind(size=tenant_name.setter('text_size'))
            
            tenant_phone = Label(
                text="电话：138-0000-0000",  # Placeholder
                font_size=dp(14),
                color=get_color_from_hex('#64748b'),
                halign='left'
            )
            tenant_phone.bind(size=tenant_phone.setter('text_size'))
            
            tenant_id = Label(
                text="身份证：123456789012345678",  # Placeholder
                font_size=dp(14),
                color=get_color_from_hex('#64748b'),
                halign='left'
            )
            tenant_id.bind(size=tenant_id.setter('text_size'))
            
            tenant_info_layout.add_widget(tenant_name)
            tenant_info_layout.add_widget(tenant_phone)
            tenant_info_layout.add_widget(tenant_id)
        else:
            no_tenant = Label(
                text="暂无租户信息",
                font_size=dp(14),
                color=get_color_from_hex('#94a3b8'),
                halign='center'
            )
            tenant_info_layout.add_widget(no_tenant)
        
        section.add_widget(tenant_info_layout)
        return section
    
    def create_rental_section(self):
        """Create rental period section"""
        section = self.create_section("📅 租赁期间")
        
        rental_layout = BoxLayout(orientation='vertical', spacing=dp(8), size_hint_y=None, height=dp(120))
        
        # Start date
        if self.room_data.get("start_date"):
            start_label = Label(
                text=f"起租日期：{self.room_data['start_date']}",
                font_size=dp(14),
                color=get_color_from_hex('#1e293b'),
                halign='left'
            )
            start_label.bind(size=start_label.setter('text_size'))
            rental_layout.add_widget(start_label)
        
        # End date
        if self.room_data.get("end_date"):
            end_label = Label(
                text=f"到期日期：{self.room_data['end_date']}",
                font_size=dp(14),
                color=get_color_from_hex('#1e293b'),
                halign='left'
            )
            end_label.bind(size=end_label.setter('text_size'))
            rental_layout.add_widget(end_label)
            
            # Calculate remaining days
            try:
                end_date = datetime.strptime(self.room_data['end_date'], "%Y-%m-%d").date()
                today = datetime.today().date()
                remaining_days = (end_date - today).days
                
                if remaining_days > 0:
                    remaining_text = f"剩余天数：{remaining_days} 天"
                    remaining_color = get_color_from_hex('#059669')
                elif remaining_days == 0:
                    remaining_text = "今日到期"
                    remaining_color = get_color_from_hex('#d97706')
                else:
                    remaining_text = f"已过期：{abs(remaining_days)} 天"
                    remaining_color = get_color_from_hex('#dc2626')
                
                remaining_label = Label(
                    text=remaining_text,
                    font_size=dp(14),
                    color=remaining_color,
                    halign='left'
                )
                remaining_label.bind(size=remaining_label.setter('text_size'))
                rental_layout.add_widget(remaining_label)
                
            except ValueError:
                pass
        
        section.add_widget(rental_layout)
        return section
    
    def create_notes_section(self):
        """Create notes section"""
        section = self.create_section("📝 备注")
        
        notes_input = TextInput(
            text=self.room_data.get('note', ''),
            hint_text="点击添加备注...",
            font_size=dp(14),
            size_hint_y=None,
            height=dp(80),
            multiline=True,
            background_color=get_color_from_hex('#f8fafc')
        )
        
        section.add_widget(notes_input)
        return section
    
    def create_section(self, title):
        """Create a section with title and content area"""
        section_layout = BoxLayout(
            orientation='vertical',
            size_hint_y=None,
            spacing=dp(8),
            padding=dp(16)
        )
        
        # Set background
        with section_layout.canvas.before:
            from kivy.graphics import Color, RoundedRectangle
            Color(*get_color_from_hex('#ffffff'))
            section_layout.rect = RoundedRectangle(
                pos=section_layout.pos,
                size=section_layout.size,
                radius=[dp(8)]
            )
        
        section_layout.bind(pos=self.update_section_rect, size=self.update_section_rect)
        
        # Title
        title_label = Label(
            text=title,
            font_size=dp(16),
            bold=True,
            color=get_color_from_hex('#1e293b'),
            size_hint_y=None,
            height=dp(30),
            halign='left'
        )
        title_label.bind(size=title_label.setter('text_size'))
        
        section_layout.add_widget(title_label)
        section_layout.height = dp(60)  # Base height, will be adjusted by content
        
        return section_layout
    
    def update_section_rect(self, instance, *args):
        """Update section background rectangle"""
        instance.rect.pos = instance.pos
        instance.rect.size = instance.size
    
    def create_action_buttons(self):
        """Create action buttons at bottom"""
        actions_layout = BoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(56),
            padding=dp(16),
            spacing=dp(8)
        )
        
        if self.room_data["status"] == "已出租":
            # Extend lease button
            extend_btn = Button(
                text="续租",
                font_size=dp(14),
                background_color=get_color_from_hex('#059669')
            )
            extend_btn.bind(on_press=self.extend_lease)
            
            # Check out button
            checkout_btn = Button(
                text="退房",
                font_size=dp(14),
                background_color=get_color_from_hex('#dc2626')
            )
            checkout_btn.bind(on_press=self.check_out)
            
            actions_layout.add_widget(extend_btn)
            actions_layout.add_widget(checkout_btn)
        else:
            # Rent out button
            rent_btn = Button(
                text="出租",
                font_size=dp(14),
                background_color=get_color_from_hex('#3b82f6')
            )
            rent_btn.bind(on_press=self.rent_out)
            actions_layout.add_widget(rent_btn)
        
        # Edit button (always available)
        edit_btn = Button(
            text="编辑",
            font_size=dp(14),
            background_color=get_color_from_hex('#64748b')
        )
        edit_btn.bind(on_press=self.edit_room)
        actions_layout.add_widget(edit_btn)
        
        return actions_layout
    
    def go_back(self, instance):
        """Go back to main screen"""
        self.manager.current = 'main'
    
    def extend_lease(self, instance):
        """Handle lease extension"""
        # TODO: Implement lease extension dialog
        print("Extend lease clicked")
    
    def check_out(self, instance):
        """Handle check out"""
        # TODO: Implement check out confirmation
        print("Check out clicked")
    
    def rent_out(self, instance):
        """Handle rent out"""
        # TODO: Implement rent out dialog
        print("Rent out clicked")
    
    def edit_room(self, instance):
        """Handle room editing"""
        # TODO: Implement room editing screen
        print("Edit room clicked")
